Option Explicit
Dim colname As String, directory As String, UserId As String, Fname As String, Fnamedata As String, Fnametext As String
Dim FnameTemp As String, rptsavepath As String, rptsuffix As String, strDate As String, FnameXls As String
Dim BeginTime As Double, hours1 As String, minutes1 As String, seconds1 As String
Dim Fnamedatadate As Date, Fnametextdate As Date
Dim rowcnt As Long, rowcnt1 As <PERSON>, lastrow As <PERSON>, lastcol As <PERSON>, colnum4 As Long
Dim wksht As Worksheet
Function OldestFile(directory, FileSpec)
' Returns the name of the oldest file in a Directory that matches the FileSpec (e.g., "*.csv").
' Returns an empty string if the directory does not exist or it contains no matching files
Dim MostRecentFile As String
Dim MostRecentDate As Date

    FileSpec = "SQB*.csv"
    Fname = Dir(directory & FileSpec, 0)
    If Fname <> "" Then
        MostRecentFile = Fname
        MostRecentDate = FileDateTime(directory & Fname)
        Do While Fname <> ""
            If FileDateTime(directory & Fname) < MostRecentDate Then
                 MostRecentFile = Fname
                 MostRecentDate = FileDateTime(directory & Fname)
             End If
             Fname = Dir
        Loop
    End If
    OldestFile = MostRecentFile
    
End Function
Function NewestTextFile(directory, FileSpec)
' Returns the name of the Newest Text file in a Directory that matches the FileSpec (e.g., "SQB *Text*.csv").
' Returns an empty string if the directory does not exist or it contains no matching files
Dim MostRecentFile As String
Dim MostRecentDate As Date

    FileSpec = "SQB *Text*.csv"
    Fname = Dir(directory & FileSpec, 0)
    If Fname <> "" Then
        MostRecentFile = Fname
        MostRecentDate = FileDateTime(directory & Fname)
        Do While Fname <> ""
            If FileDateTime(directory & Fname) > MostRecentDate And UCase(Fname) Like "*TEXT*" Then
                 MostRecentFile = Fname
                 MostRecentDate = FileDateTime(directory & Fname)
             End If
             Fname = Dir
        Loop
    End If
    NewestTextFile = MostRecentFile
    
End Function
Function NewestDataFile(directory, FileSpec)
' Returns the name of the Newest call data file in a Directory that matches the FileSpec (e.g., "SQB*.csv").
' Returns an empty string if the directory does not exist or it contains no matching files
Dim MostRecentFile As String
Dim MostRecentDate As Date

    FileSpec = "SQB*.csv"
    Fname = Dir(directory & FileSpec, 0)
    If Fname <> "" Then
        MostRecentFile = Fname
        MostRecentDate = FileDateTime(directory & Fname)
        Do While Fname <> ""
            If FileDateTime(directory & Fname) > MostRecentDate And Not UCase(Fname) Like "*TEXT*" Then
                 MostRecentFile = Fname
                 MostRecentDate = FileDateTime(directory & Fname)
             End If
             Fname = Dir
        Loop
    End If
    NewestDataFile = MostRecentFile
    
End Function
Sub Toggle_Hands_Free()

Dim Msg, Style, Title, response
    If [HANDS_FREE11] <> "YES" Then
    Msg = "Hands free mode is currently ""DISABLED""" & Chr(10) & Chr(10) _
    & "This file will open to the ""SETUP (START)"" Sheet without automatically loading the source data." & Chr(10) & Chr(10) _
    & "WOULD YOU LIKE TO ENABLE ""HANDS FREE"" MODE?"
    Style = vbYesNo + vbQuestion + vbDefaultButton1
    Title = "ENABLE HANDS FREE MODE"
    response = MsgBox(Msg, Style, Title)
        If response = vbYes Then
        [HANDS_FREE11] = "YES"
        Sheets("SETUP (START)").Shapes("Toggle Hands Free").TextFrame.Characters.Text = "DISABLE HANDS FREE MODE"
        ThisWorkbook.Save
        MsgBox ("Hands free mode is now ""ENABLED""" & Chr(10) & Chr(10) _
        & "The most recent source BI SQB Data and SQB Text files will be automatically selected and ready to load each time this file is opened."), vbInformation
        End If
    ElseIf [HANDS_FREE11] = "YES" Then
    Msg = "Hands free mode is currently ""ENABLED""" & Chr(10) & Chr(10) _
    & "The most recent source BI SQB Data and SQB Text files will be automatically selected and ready to load each time this file is opened." & Chr(10) & Chr(10) _
    & "WOULD YOU LIKE TO DISABLE ""HANDS FREE"" MODE?"
    Style = vbYesNo + vbQuestion + vbDefaultButton1
    Title = "DISABLE HANDS FREE MODE"
    response = MsgBox(Msg, Style, Title)
        If response = vbYes Then
        [HANDS_FREE11] = "NO"
        Sheets("SETUP (START)").Shapes("Toggle Hands Free").TextFrame.Characters.Text = "ENABLE HANDS FREE MODE"
        ThisWorkbook.Save
        MsgBox ("Hands free mode is now ""DISABLED""" & Chr(10) & Chr(10) _
        & "This file will now open to the ""SETUP (START)"" Sheet without automatically loading the source data."), vbInformation
        End If
    End If
    
    ThisWorkbook.Save

End Sub
Sub Toggle_Autosave()

    Get_Newest_CSV_Data_File
    Application.EnableEvents = False
    Get_Newest_CSV_Text_File
    
    On Error Resume Next
    Fnamedatadate = FileDateTime(directory & Fnamedata)
    Fnametextdate = FileDateTime(directory & Fnametext)
    
    strDate = Format(Now, "MM-DD-YY")
    strDate = Format(Fnamedatadate, "MM-DD-YY HHMM AM/PM")
    On Error GoTo -1
    On Error GoTo 0
    
    If InStr(UCase([LAST_CSV_DATA_FILE10]), "TEMPLATE") > 0 Then
    rptsuffix = Mid([LAST_CSV_DATA_FILE10], 7, WorksheetFunction.Search("TEMPLATE", [LAST_CSV_DATA_FILE10]) - 8)
    Else
    rptsuffix = Mid([LAST_CSV_DATA_FILE10], 7, WorksheetFunction.Search(".CSV", [LAST_CSV_DATA_FILE10]) - 7)
    End If
    
    If ThisWorkbook.Path = "C:\SideKick For BI\ExcelTmplt" Then
    rptsavepath = "C:\SideKick For BI\ExcelReports\"
    Else
    rptsavepath = ThisWorkbook.Path & "\"
    End If
    
    directory = ""
    Fname = ""
    If [REPORT_SAVE_PATH11] <> "" And Right([REPORT_SAVE_PATH11], 1) <> "\" Then
    rptsavepath = [REPORT_SAVE_PATH11] & "\"
    ElseIf [REPORT_SAVE_PATH11] <> "" Then rptsavepath = [REPORT_SAVE_PATH11]
    End If
    directory = Dir(rptsavepath, vbDirectory)
    If directory = "" Then
    rptsavepath = ThisWorkbook.Path & "\"
    End If
    If [REPORT_SAVE_NAME11] <> "" Then
    Fname = [REPORT_SAVE_NAME11]
    Else: Fname = "BI SideKick Call History Report " & rptsuffix & " " & strDate
    End If

Dim Msg, Style, Title, response
    If [AUTOSAVE11] <> "YES" Then
    Msg = "Autosave mode is currently ""DISABLED""" & Chr(10) & Chr(10) _
    & "After the update has completed, you will be prompted to save the file" & Chr(10) _
    & "to a folder and file name of your choosing." & Chr(10) & Chr(10) _
    & "Would you like to ENABLE ""AUTOSAVE"" mode?"
    Style = vbYesNo + vbQuestion + vbDefaultButton1
    Title = "ENABLE AUTOSAVE MODE"
    response = MsgBox(Msg, Style, Title)
        If response = vbYes Then
        [AUTOSAVE11] = "YES"
        Sheets("SETUP (START)").Shapes("Toggle Autosave").TextFrame.Characters.Text = "DISABLE AUTOSAVE MODE"
            If directory = "" Then
            MsgBox ("Autosave mode is now ""ENABLED""" & Chr(10) & Chr(10) _
            & "The Report Save Path entered below does not exist." & Chr(10) _
            & "Therefore the report will be autosaved to the current working folder:" & Chr(10) _
            & rptsavepath & Chr(10) & Chr(10) _
            & "With filename:" & Chr(10) _
            & Fname), vbInformation
            Else
            MsgBox ("Autosave mode is now ""ENABLED""" & Chr(10) & Chr(10) _
            & "The report file will be autosaved to folder:" & Chr(10) _
            & rptsavepath & Chr(10) & Chr(10) _
            & "With filename:" & Chr(10) _
            & Fname), vbInformation
            End If
        End If
    ElseIf [AUTOSAVE11] = "YES" Then
            If directory = "" Then
            Msg = "Autosave mode is currently ""ENABLED""" & Chr(10) & Chr(10) _
            & "The Report Save Path entered below does not exist." & Chr(10) _
            & "Therefore the report will be autosaved to the current working folder:" & Chr(10) _
            & rptsavepath & Chr(10) & Chr(10) _
            & "With filename:" & Chr(10) _
            & Fname & Chr(10) & Chr(10) _
            & "Would you like to DISABLE ""AUTOSAVE"" mode?"
            Style = vbYesNo + vbQuestion + vbDefaultButton1
            Title = "DISABLE AUTOSAVE MODE"
            response = MsgBox(Msg, Style, Title)
            Else
            Msg = "Autosave mode is currently ""ENABLED""" & Chr(10) & Chr(10) _
            & "The report file will be autosaved to folder:" & Chr(10) _
            & rptsavepath & Chr(10) & Chr(10) _
            & "With filename:" & Chr(10) _
            & Fname & Chr(10) & Chr(10) _
            & "Would you like to DISABLE ""AUTOSAVE"" mode?"
            Style = vbYesNo + vbQuestion + vbDefaultButton1
            Title = "DISABLE AUTOSAVE MODE"
            response = MsgBox(Msg, Style, Title)
            End If
            If response = vbYes Then
            [AUTOSAVE11] = "NO"
            Sheets("SETUP (START)").Shapes("Toggle Autosave").TextFrame.Characters.Text = "ENABLE AUTOSAVE MODE"
            MsgBox ("Autosave mode is now ""DISABLED""" & Chr(10) & Chr(10) _
            & "After the update has completed, you will be prompted to save the file" & Chr(10) _
            & "to a folder and file name of your choosing."), vbInformation
            End If
    End If

    ThisWorkbook.Save
    
End Sub
Sub Auto_Update_Report()

' Runs when workbook is opened and macros are enabled
    [CALC_MODE_10] = Application.Calculation
    
    Clear_Sheets
    
    Application.DisplayAlerts = False
    Application.EnableEvents = False
    Application.ScreenUpdating = False
    Application.Calculation = xlManual

    [DIR_MAIN_10].ClearContents
    [DIR1_10].ClearContents
    [DIR2_10].ClearContents
    [DIR3_10].ClearContents
    
    If Dir("C:\SideKick For BI\", vbDirectory) = "" Then
        MkDir "C:\SideKick For BI\"
        [DIR_MAIN_10] = "C:\SideKick For BI\"
    End If
    If Dir("C:\SideKick For BI\ExcelSetup\", vbDirectory) = "" Then
        MkDir "C:\SideKick For BI\ExcelSetup\"
        [DIR1_10] = "C:\SideKick For BI\ExcelSetup\"
    End If
    If Dir("C:\SideKick For BI\ExcelTmplt\", vbDirectory) = "" Then
        MkDir "C:\SideKick For BI\ExcelTmplt\"
        [DIR2_10] = "C:\SideKick For BI\ExcelTmplt\"
        ThisWorkbook.SaveAs Filename:="C:\SideKick For BI\ExcelTmplt\Template_BI_SideKick_Report.xlsb"
    End If
    If Dir("C:\SideKick For BI\ExcelReports\", vbDirectory) = "" Then
        MkDir "C:\SideKick For BI\ExcelReports\"
        [DIR3_10] = "C:\SideKick For BI\ExcelReports\"
    End If
    
    BeginTime = Timer
    
    Application.DisplayAlerts = False
    
    Get_Newest_CSV_Data_File
    Application.EnableEvents = False
    
    Get_Newest_CSV_Text_File
    Application.EnableEvents = False
    
    On Error Resume Next
    Fnamedatadate = FileDateTime(directory & Fnamedata)
    Fnametextdate = FileDateTime(directory & Fnametext)
    On Error GoTo 0
    
'    GoTo skipto
Dim Msg, Style, Title, response
    If Fnametext = "" Then
    Msg = "This will load the most recent SQB source call data file : " & Chr(10) & directory & Fnamedata & Chr(10) _
    & "Dated: " & Fnamedatadate & Chr(10) & Chr(10) _
    & "NO CALL TEXT WILL BE LOADED." & Chr(10) & Chr(10) _
    & "PLEASE VERIFY THE FILES LISTED ABOVE AND IF CORRECT, CLICK OK TO CONTINUE." & Chr(10) & Chr(10) _
    & "CLICK CANCEL TO OPEN THE REPORT TEMPLATE WITHOUT LOADING DATA."
    Style = vbOKCancel + vbInformation + vbDefaultButton1
    Title = "LOAD DATA / CREATE REPORT"
    response = MsgBox(Msg, Style, Title)
    If response = vbCancel Then
    Application.StatusBar = ""
    Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
    Application.EnableEvents = True
    Application.Calculation = [CALC_MODE_10]
    Exit Sub
    End If
    Else
    Msg = "This will load the most recent SQB source call data file : " & Chr(10) & directory & Fnamedata & Chr(10) _
    & "Dated: " & Fnamedatadate & Chr(10) & Chr(10) _
    & "and load the most recent SQB source call text from : " & Chr(10) & directory & Fnametext & Chr(10) _
    & "Dated: " & Fnametextdate & Chr(10) & Chr(10) _
    & "PLEASE VERIFY THE FILES LISTED ABOVE AND IF CORRECT, CLICK OK TO CONTINUE." & Chr(10) & Chr(10) _
    & "CLICK CANCEL TO OPEN THE REPORT TEMPLATE WITHOUT LOADING DATA."
    Style = vbOKCancel + vbInformation + vbDefaultButton1
    Title = "LOAD DATA / CREATE REPORT"
    response = MsgBox(Msg, Style, Title)
    If response = vbCancel Then
    Application.StatusBar = ""
    Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
    Application.EnableEvents = True
    Application.Calculation = [CALC_MODE_10]
    Exit Sub
    End If
    End If
    
skipto:
    FnameTemp = "C:\SideKick For BI\ExcelTmplt\Temp_Delete_BI_SideKick_Report" & ".xlsb"
    ThisWorkbook.SaveAs Filename:=FnameTemp
    
    Sheets("Data").Visible = True
    
    Sheets("SETUP (START)").Select
    Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = True
    Application.ScreenUpdating = True
    Application.wait (Now + TimeValue("0:00:01"))

    Load_Call_Text_CSV
    Application.EnableEvents = False
    
    Load_Call_Data_CSV
    Application.EnableEvents = False
    
    Remove_Empty_Data_Columns
    Application.EnableEvents = False
    
Dim colname As String
    Range(Sheets("Data").Range("A1"), Sheets("Data").Cells(1, Sheets("Data").Columns.Count).End(xlToLeft)).Name = "COLUMN_HEADINGS1"

    Application.StatusBar = "PARSING PARTS"
    Parse_Parts
    Application.EnableEvents = False
    
    Dim connection
    For Each connection In ThisWorkbook.Connections
    If connection.Name Like "Connection*" Then connection.Delete
    Next connection
        
    Setup_Report
    Application.EnableEvents = False
    
    Application.StatusBar = ""
    
    On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 5
    
    Set_Font
    
    If [DIR2_10] <> "" Then MsgBox ("Folder: ""C:\SideKick For BI\ExcelTmplt\"" created.  The ""Template_BI_SideKick_Report" & ".xlsb"" file has been saved to this folder."), vbInformation
    
    hours1 = Format(DateAdd("s", (Timer - BeginTime), "00:00:00"), "H")
    If hours1 = 0 Then hours1 = "" Else If hours1 = 1 Then hours1 = hours1 & " HOUR, " Else hours1 = hours1 & " HOURS, "
    minutes1 = Format(DateAdd("s", (Timer - BeginTime), "00:00:00"), "N")
    If minutes1 = 0 Then minutes1 = "" Else If minutes1 = 1 Then minutes1 = minutes1 & " MINUTE, " Else minutes1 = minutes1 & " MINUTES, "
    seconds1 = Format(DateAdd("s", (Timer - BeginTime), "00:00:00"), "S")
    If seconds1 = 1 Then seconds1 = seconds1 & " SECOND" Else seconds1 = seconds1 & " SECONDS"
        
    If InStr(UCase([LAST_CSV_DATA_FILE10]), "TEMPLATE") > 0 Then
    rptsuffix = Mid([LAST_CSV_DATA_FILE10], 7, WorksheetFunction.Search("TEMPLATE", [LAST_CSV_DATA_FILE10]) - 8)
    Else
    rptsuffix = Mid([LAST_CSV_DATA_FILE10], 7, WorksheetFunction.Search(".CSV", [LAST_CSV_DATA_FILE10]) - 7)
    End If
    
    If ThisWorkbook.Path = "C:\SideKick For BI\ExcelTmplt" Then
    rptsavepath = "C:\SideKick For BI\ExcelReports\"
    Else
    rptsavepath = ThisWorkbook.Path & "\"
    End If
    
    [TEMPLT_NAME_VER_11] = "BI SideKick Call History Report v" & [VERSION_10]
    
    Sheets("Data").Visible = False
    
    Sheets("Report").Select
    
    Application.DisplayAlerts = False
    Application.ScreenUpdating = True
    ThisWorkbook.SaveAs Filename:=rptsavepath & "BI SideKick Call History Report.xlsb"
    Kill FnameTemp
    
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    strDate = Format(Now, "MM-DD-YY")
    strDate = Format(Fnamedatadate, "MM-DD-YY HHMM AM/PM")
    On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 5
    
    For Each wksht In Worksheets
         wksht.Cells.Font.Size = Sheets("SETUP (START)").TextBox1.Value
'         wksht.Cells.Font.Size = 8
'         wksht.Cells.Font.Name = "Arial"
    Next wksht

    Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
    Sheets("SETUP (START)").Shapes("Toggle Hands Free").Visible = True
   
    directory = ""
    Fname = ""
    If [REPORT_SAVE_PATH11] <> "" And Right([REPORT_SAVE_PATH11], 1) <> "\" Then
    rptsavepath = [REPORT_SAVE_PATH11] & "\"
    ElseIf [REPORT_SAVE_PATH11] <> "" Then rptsavepath = [REPORT_SAVE_PATH11]
    End If
    
    directory = Dir(rptsavepath, vbDirectory)
    If directory = "" Then
    MsgBox ("The report save path is not invalid. File will be saved to this workbook's current folder.")
    rptsavepath = ThisWorkbook.Path & "\"
    End If
    
    If [REPORT_SAVE_NAME11] <> "" Then
    Fname = [REPORT_SAVE_NAME11]
    Else: Fname = "BI SideKick Call History Report " & rptsuffix & " " & strDate
    End If
    FnameXls = rptsavepath & Fname
    
    Application.DisplayAlerts = True
    Application.StatusBar = "SAVING FILE"
    If [AUTOSAVE10] = "YES" Then
    ThisWorkbook.SaveAs Filename:=FnameXls
    MsgBox ("!!!Process Complete!!!" & Chr(10) & Chr(10) & "This report has been saved to folder:" & Chr(10) & """" & rptsavepath & """" & Chr(10) & Chr(10) & _
    "with filename:" & Chr(10) & """" & Fname & """") & Chr(10) & Chr(10) _
    & "ELAPSED TIME: " & hours1 & minutes1 & seconds1, vbInformation
    Else
    MsgBox ("!!!Process Complete!!!" & Chr(10) & Chr(10) _
    & "ELAPSED TIME: " & hours1 & minutes1 & seconds1 & Chr(10) & Chr(10) _
    & "PLEASE MANUALLY SAVE THE FILE TO THE FOLDER OF YOUR CHOICE"), vbInformation
    Fname = Application.GetSaveAsFilename(FileFilter:="Excel Binary Workbook (*.xlsb), *.xlsb", Title:="Save File", InitialFileName:=ThisWorkbook.Path & "")
    If Fname <> "False" Then ThisWorkbook.SaveAs Fname
    End If
    
    Application.StatusBar = ""
    Application.EnableEvents = True
    Application.Calculation = [CALC_MODE_10]
    Application.WindowState = xlMaximized

    Exit Sub
    
5:  On Error GoTo -1
    On Error GoTo 0
    Sheets("Data").Visible = False
    
    MsgBox ("Report Not Autosaved.  Please manually save the report."), vbInformation
    If [DIR2_10] <> "" Then MsgBox ("Folder: ""C:\SideKick For BI\ExcelTmplt\"" created.  The ""Template_BI_SideKick_Report" & ".xlsb"" file has been saved to this folder."), vbInformation
    
    Application.StatusBar = ""
    Application.EnableEvents = True
    Application.Calculation = [CALC_MODE_10]
    Application.WindowState = xlMaximized
    
End Sub
Sub Reload_Update_Report()

    [CALC_MODE_10] = Application.Calculation
    
    Application.DisplayAlerts = False
    Application.EnableEvents = False
    Application.ScreenUpdating = False
    
    Fnamedata = ""
    Fnametext = ""
    UserId = (Environ$("Username"))
    
    If UCase([FILE_LOCATION11]) = "DEFAULT" Then
        directory = "C:\Users\<USER>\Diebold Nixdorf\Downloads\"
        Else
        directory = [FILE_LOCATION11]
        If Right(directory, 1) <> "\" Then directory = directory & "\"
    End If
    
Dim Msg, Style, Title, response
    Msg = "This will load new data and replace any existing data." & Chr(10) & Chr(10) _
    & "TWO file open dialogs will display back to back after you click OK." & Chr(10) & Chr(10) _
    & "First select the CSV ""CALL DATA"" source file," & Chr(10) & "then select the CSV ""CALL TEXT"" source file"
    Style = vbOKCancel + vbExclamation + vbDefaultButton2
    Title = "LOAD DATA FROM SOURCE"
    response = MsgBox(Msg, Style, Title)
        If response = vbCancel Then
            Application.StatusBar = ""
            Application.EnableEvents = True
            Exit Sub
        End If
    
    Select_SQB_CSV_Data_File
    Select_SQB_CSV_Text_File
    
    If Fnamedata <> "" Then Fnamedatadate = FileDateTime("C:\Users\<USER>\Diebold Nixdorf\wip\regex obie sybil proj\SQB - 000 Default Template (4) 1.csv")
    If Fnametext <> "" Then Fnametextdate = FileDateTime("C:\Users\<USER>\Diebold Nixdorf\wip\regex obie sybil proj\SQB - Text (4) 1.csv")
    
    If Fnametext = "" Then
    Msg = "This will load the source call data from : " & Chr(10) & directory & Fnamedata & Chr(10) _
    & "Dated: " & Fnamedatadate & Chr(10) & Chr(10) _
    & "NO CALL TEXT WILL BE LOADED." & Chr(10) & Chr(10) _
    & "                                     !!!WARNING!!!" & Chr(10) & Chr(10) _
    & "THIS WILL CLEAR ANY EXISTING DATA IN THIS FILE AND BUILD A NEW REPORT." & Chr(10) & Chr(10) _
    & "DO YOU WISH TO CONTINUE?"
    Style = vbYesNo + vbExclamation + vbDefaultButton2
    Title = "LOAD DATA / UPDATE REPORT"
    response = MsgBox(Msg, Style, Title)
        If response = vbNo Then
            Application.StatusBar = ""
            Application.EnableEvents = True
            Exit Sub
        End If
    Else
        Msg = "This will load the source call data from : " & Chr(10) & directory & Fnamedata & Chr(10) _
        & "Dated: " & Fnamedatadate & Chr(10) & Chr(10) _
        & "and the source call text from : " & Chr(10) & directory & Fnametext & Chr(10) _
        & "Dated: " & Fnametextdate & Chr(10) & Chr(10) _
        & "                                     !!!WARNING!!!" & Chr(10) & Chr(10) _
        & "THIS WILL CLEAR ALL CURRENT DATA AND BUILD A NEW REPORT." & Chr(10) & Chr(10) _
        & "DO YOU WISH TO CONTINUE?"
        Style = vbYesNo + vbExclamation + vbDefaultButton2
        Title = "LOAD DATA / UPDATE REPORT"
        response = MsgBox(Msg, Style, Title)
            If response = vbNo Then
            Application.StatusBar = ""
            Application.EnableEvents = True
            Exit Sub
            End If
    End If
    
    BeginTime = Timer
    
    Clear_Sheets
    
    Application.Calculation = xlManual
    Application.DisplayAlerts = False
    Application.EnableEvents = False
    Application.ScreenUpdating = False
    
    [DIR_MAIN_10].ClearContents
    [DIR1_10].ClearContents
    [DIR2_10].ClearContents
    [DIR3_10].ClearContents
    
    If Dir("C:\SideKick For BI\", vbDirectory) = "" Then
        MkDir "C:\SideKick For BI\"
        [DIR_MAIN_10] = "C:\SideKick For BI\"
    End If
    If Dir("C:\SideKick For BI\ExcelSetup\", vbDirectory) = "" Then
        MkDir "C:\SideKick For BI\ExcelSetup\"
        [DIR1_10] = "C:\SideKick For BI\ExcelSetup\"
    End If
    If Dir("C:\SideKick For BI\ExcelTmplt\", vbDirectory) = "" Then
        MkDir "C:\SideKick For BI\ExcelTmplt\"
        [DIR2_10] = "C:\SideKick For BI\ExcelTmplt\"
        ThisWorkbook.SaveAs Filename:="C:\SideKick For BI\ExcelTmplt\Template_BI_SideKick_Report.xlsb"
    End If
    If Dir("C:\SideKick For BI\ExcelReports\", vbDirectory) = "" Then
        MkDir "C:\SideKick For BI\ExcelReports\"
        [DIR3_10] = "C:\SideKick For BI\ExcelReports\"
'        MsgBox ("FOLDER: ""C:\SideKick For BI\ExcelReports\"" has been created.  Your BI SideKick Call History Reports will be saved here."), vbInformation
    End If

    Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = True
    Application.ScreenUpdating = True
    Application.wait (Now + TimeValue("0:00:01"))
    
    FnameTemp = "C:\SideKick For BI\ExcelTmplt\Temp_Delete_BI_SideKick_Report" & ".xlsb"
    ThisWorkbook.SaveAs Filename:=FnameTemp
    
    Load_Call_Text_CSV
    Application.EnableEvents = False
    
    Load_Call_Data_CSV
    Application.EnableEvents = False
    
    Remove_Empty_Data_Columns
    Application.EnableEvents = False
    
    Application.StatusBar = "PARSING PARTS"
    Parse_Parts
    Application.EnableEvents = False
    
    Dim connection
    For Each connection In ThisWorkbook.Connections
    If connection.Name Like "Connection*" Then connection.Delete
    Next connection
        
    Setup_Report
    Application.EnableEvents = False
    
    Application.StatusBar = ""
    
    On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 5
    
    For Each wksht In Worksheets
         wksht.Cells.Font.Size = Sheets("SETUP (START)").TextBox1.Value
'         wksht.Cells.Font.Size = 8
'         wksht.Cells.Font.Name = "Arial"
    Next wksht

    Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
    Sheets("SETUP (START)").Shapes("Toggle Hands Free").Visible = True
    
    Set_Font
    
    If [DIR2_10] <> "" Then MsgBox ("Folder: ""C:\SideKick For BI\ExcelTmplt\"" created.  The ""Template_BI_SideKick_Report" & ".xlsb"" file has been saved to this folder."), vbInformation
    
    hours1 = Format(DateAdd("s", (Timer - BeginTime), "00:00:00"), "H")
    If hours1 = 0 Then hours1 = "" Else If hours1 = 1 Then hours1 = hours1 & " HOUR, " Else hours1 = hours1 & " HOURS, "
    minutes1 = Format(DateAdd("s", (Timer - BeginTime), "00:00:00"), "N")
    If minutes1 = 0 Then minutes1 = "" Else If minutes1 = 1 Then minutes1 = minutes1 & " MINUTE, " Else minutes1 = minutes1 & " MINUTES, "
    seconds1 = Format(DateAdd("s", (Timer - BeginTime), "00:00:00"), "S")
    If seconds1 = 1 Then seconds1 = seconds1 & " SECOND" Else seconds1 = seconds1 & " SECONDS"
        
    If InStr(UCase([LAST_CSV_DATA_FILE10]), "TEMPLATE") > 0 Then
    rptsuffix = Mid([LAST_CSV_DATA_FILE10], 7, WorksheetFunction.Search("TEMPLATE", [LAST_CSV_DATA_FILE10]) - 8)
    Else
    rptsuffix = Mid([LAST_CSV_DATA_FILE10], 7, WorksheetFunction.Search(".CSV", [LAST_CSV_DATA_FILE10]) - 7)
    End If
    
    If ThisWorkbook.Path = "C:\SideKick For BI\ExcelTmplt" Then
    rptsavepath = "C:\SideKick For BI\ExcelReports\"
    Else
    rptsavepath = ThisWorkbook.Path & "\"
    End If
    
    [TEMPLT_NAME_VER_11] = "BI SideKick Call History Report v" & [VERSION_10]
    
    Sheets("Data").Visible = False
    
    Sheets("Report").Select
    
    Application.DisplayAlerts = False
    Application.ScreenUpdating = True
    ThisWorkbook.SaveAs Filename:=rptsavepath & "BI SideKick Call History Report.xlsb"
    Kill FnameTemp
    
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    strDate = Format(Now, "MM-DD-YY")
    strDate = Format(Fnamedatadate, "MM-DD-YY HHMM AM/PM")
    On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 5
    
    directory = ""
    Fname = ""
    If [REPORT_SAVE_PATH11] <> "" And Right([REPORT_SAVE_PATH11], 1) <> "\" Then
    rptsavepath = [REPORT_SAVE_PATH11] & "\"
    ElseIf [REPORT_SAVE_PATH11] <> "" Then rptsavepath = [REPORT_SAVE_PATH11]
    End If
    
    directory = Dir(rptsavepath, vbDirectory)
    If directory = "" Then
    MsgBox ("The report save path is not invalid. File will be saved to this workbook's current folder.")
    rptsavepath = ThisWorkbook.Path & "\"
    End If
    
    If [REPORT_SAVE_NAME11] <> "" Then
    Fname = [REPORT_SAVE_NAME11]
    Else: Fname = "BI SideKick Call History Report " & rptsuffix & " " & strDate
    End If
    FnameXls = rptsavepath & Fname
    
    Application.DisplayAlerts = True
    Application.StatusBar = "SAVING FILE"
    If [AUTOSAVE10] = "YES" Then
    MsgBox ("!!!Process Complete!!!" & Chr(10) & Chr(10) & "This report will be saved in folder:" & Chr(10) & """" & rptsavepath & """" & Chr(10) & Chr(10) & _
    "with filename:" & Chr(10) & """" & Fname & """") & Chr(10) & Chr(10) _
    & "ELAPSED TIME: " & hours1 & minutes1 & seconds1, vbInformation
    ThisWorkbook.SaveAs Filename:=FnameXls
    Else
    MsgBox ("!!!Process Complete!!!" & Chr(10) & Chr(10) _
    & "ELAPSED TIME: " & hours1 & minutes1 & seconds1 & Chr(10) & Chr(10) _
    & "PLEASE MANUALLY SAVE THE FILE TO THE FOLDER OF YOUR CHOICE"), vbInformation
    Fname = Application.GetSaveAsFilename(FileFilter:="Excel Binary Workbook (*.xlsb), *.xlsb", Title:="Save File", InitialFileName:=ThisWorkbook.Path & "")
    If Fname <> "False" Then ThisWorkbook.SaveAs Fname
    End If
    
    Application.StatusBar = ""
    Application.EnableEvents = True
    Application.Calculation = [CALC_MODE_10]
    
    Exit Sub
    
5:  On Error GoTo -1
    On Error GoTo 0
    Sheets("Data").Visible = False
    
    MsgBox ("Report Not Autosaved.  Please manually save the report."), vbInformation
    If [DIR2_10] <> "" Then MsgBox ("Folder: ""C:\SideKick For BI\ExcelTmplt\"" created.  The ""Template_BI_SideKick_Report" & ".xlsb"" file has been saved to this folder."), vbInformation
    
    Application.StatusBar = ""
    Application.EnableEvents = True
    Application.Calculation = [CALC_MODE_10]
    
End Sub
Sub Get_Newest_CSV_Data_File()
    
    Application.DisplayAlerts = False
    Application.EnableEvents = False
    Application.ScreenUpdating = False

    UserId = (Environ$("Username"))
    
    If UCase([FILE_LOCATION11]) = "DEFAULT" Then
    directory = "C:\Users\<USER>\downloads\"
    Else
    directory = [FILE_LOCATION11]
    If Right(directory, 1) <> "\" Then directory = directory & "\"
    End If
    
    Fnamedata = NewestDataFile(directory, "SQB*.csv")
    
    If Fnamedata = "" Then
    MsgBox ("No SQB*.csv call data file was found." & Chr(10) & Chr(10) & "You must first export a BI SQB call data report as type Data -> CSV Format to folder:" & Chr(10) & Chr(10) _
    & directory & Chr(10) & Chr(10) _
    & " Click the green ""LOAD DATA FROM SOURCE"" button at the top of the sheet when ready to retry."), vbCritical
    Sheets("SETUP (START)").Select
    Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
    Application.StatusBar = ""
    Application.EnableEvents = True
    End
    End If
    
' ***********************SAVE LATEST FILE ONLY**********************
'Dim basename As String
'    On Error Resume Next
'    Do
'    UCase(Fnamedata) = UCase(OldestFile(directory, "SQB*(*).csv"))
'    If UCase(Fnamedata) Like "*(?).CSV" Then
'        basename = Left(Fnamedata, Len(Fnamedata) - 7)
'        Kill directory & basename & ".csv"
'        Name directory & Fnamedata As directory & basename & ".csv"
'    ElseIf UCase(Fnamedata) Like "*(??).CSV" Then
'        basename = Left(Fnamedata, Len(Fnamedata) - 8)
'        Kill directory & basename & ".csv"
'        Name directory & Fnamedata As directory & basename & ".csv"
'    End If
'    Loop Until Fnamedata = ""
' ***********************END SAVE LATEST FILE ONLY******************

    [FILENAME_DATA10] = directory & Fnamedata
    
    Application.ScreenUpdating = False

End Sub
Sub Select_SQB_CSV_Data_File()
    
    Application.DisplayAlerts = False
    Application.EnableEvents = False
    Application.ScreenUpdating = False

    UserId = (Environ$("Username"))
    
    If UCase([FILE_LOCATION11]) = "DEFAULT" Then
    directory = "C:\Users\<USER>\downloads\"
    Else
    directory = [FILE_LOCATION11]
    If Right(directory, 1) <> "\" Then directory = directory & "\"
    End If
    
    Fnamedata = NewestDataFile(directory, "SQB*.csv")

Dim fDialog As FileDialog, result As Integer
1:  Set fDialog = Application.FileDialog(msoFileDialogOpen)
     
'Optional: FileDialog properties
    fDialog.AllowMultiSelect = False
    fDialog.Title = "PLEASE SELECT THE BI SIDEKICK SQB CALL DATA CSV FILE TO IMPORT."
    fDialog.InitialFileName = directory & Fnamedata
     
'Optional: Add filters
    fDialog.Filters.Clear
    fDialog.Filters.Add "Text/CSV files", "*.csv"
'    fDialog.Filters.Add "Excel files", "*.xlsx"
'    fDialog.Filters.Add "All files", "*.*"
'    fDialog.Filters.Add "Excel files", "*.xlsx;*.xls;*.xlsm"
   
    If fDialog.Show = -1 Then
        Fnamedata = Dir(fDialog.SelectedItems(1))
        GoTo 2
    End If
    GoTo 3
    
2:  If Not UCase(Fnamedata) Like "SQB*.CSV" Or UCase(Fnamedata) Like "*TEXT*.CSV" Then
        MsgBox ("The file selected for import does not appear to be a valid BI SideKick CSV call data file.  Valid BI SideKick call data files MUST begin with ""SQB"" and MUST NOT have the word ""Text"" in the filename." & Chr(10) & Chr(10) & _
        "Please select a valid SQB call data file." & Chr(10) & Chr(10) & _
        "Current BI export folder and path =: " & directory), vbCritical
        GoTo 1
    End If
    
    [FILENAME_DATA10] = directory & Fnamedata
    
    Set fDialog = Nothing
    Application.StatusBar = ""
    Application.EnableEvents = True
    Exit Sub
    
3:  On Error GoTo -1
    On Error GoTo 0
    Sheets("SETUP (START)").Select
    Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
    [TEMPLT_NAME_VER_11].Offset(7, 0).Select
    Set fDialog = Nothing
    Application.StatusBar = ""
    Application.EnableEvents = True
    
End Sub
Sub Load_Call_Data_CSV()

    Application.DisplayStatusBar = True
    Application.StatusBar = "LOADING CALL DATA"
    
    Application.DisplayAlerts = False
    Application.EnableEvents = False
    Application.ScreenUpdating = False

    UserId = (Environ$("Username"))
    
    If [FILE_LOCATION11] = "" Then [FILE_LOCATION11] = "DEFAULT"
    If UCase([FILE_LOCATION11]) = "DEFAULT" Then
        directory = "C:\Users\<USER>\downloads\"
    Else
    directory = [FILE_LOCATION11]
    If Right(directory, 1) <> "\" Then directory = directory & "\"
    End If
    
    If Not UCase(Fnamedata) Like "SQB*.CSV" Or UCase(Fnamedata) Like "SQB *TEXT*.CSV" Then
        MsgBox ("The file selected for import does not appear to be a valid BI SideKick CSV call data file.  Valid BI SideKick Call Data files begin with ""SQB"" and MUST NOT have the word ""Text"" in the filename." & Chr(10) & Chr(10) & _
        "Please select a valid BI SQB call data CSV file." & Chr(10) & Chr(10) & _
        "Current BI export folder and path =: " & directory), vbExclamation
        Sheets("SETUP (START)").Select
        Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
        Application.StatusBar = ""
        Application.EnableEvents = True
        End
    End If
    If [FILENAME_DATA10] = "" Or UCase(Dir([FILENAME_DATA10])) <> UCase(Dir(directory & Fnamedata)) Then
        MsgBox ("Unable to load the source call data.  Cannot locate the CSV call data file required.  Make sure the BI report results are exported as ""Data -> CSV Format"" to your: " & directory & " folder, then try again." _
        & "  If problems continue, make sure the BI Export File Location is correct, click the green ""LOAD DATA FROM SOURCE"" button at the top of the sheet, then select the files to import."), vbExclamation
        Sheets("SETUP (START)").Select
        Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
        Application.StatusBar = ""
        Application.EnableEvents = True
        End
    End If
    
    Sheets("Data").Visible = True
    Sheets("Data").Select
    With Sheets("Data").ListObjects.Add(SourceType:=0, Source:= _
        "OLEDB;Provider=Microsoft.Mashup.OleDb.1;Data Source=$Workbook$;Location=SQBCalls;Extended Properties=""""" _
        , Destination:=Range("$A$1")).QueryTable
        .CommandType = xlCmdSql
        .CommandText = Array("SELECT * FROM [SQBCalls]")
        .RowNumbers = False
        .FillAdjacentFormulas = False
        .PreserveFormatting = False
        .RefreshOnFileOpen = False
        .BackgroundQuery = False
        .RefreshStyle = xlInsertDeleteCells
        .SavePassword = False
        .SaveData = False
        .AdjustColumnWidth = False
        .RefreshPeriod = 0
        .PreserveColumnInfo = False
        .ListObject.DisplayName = "SQBCalls"
        .Refresh BackgroundQuery:=False
    End With
    ActiveSheet.ListObjects("SQBCalls").TableStyle = ""
    ActiveSheet.ListObjects("SQBCalls").ShowTableStyleRowStripes = False
    
'    ActiveSheet.ListObjects("SQBCalls").Unlist
    
    Dim rangename
    For Each rangename In ThisWorkbook.Sheets("Data").Names
    If rangename.Name Like "*SQB*" Then rangename.Delete
    Next rangename
    
    Define_Names_Data
    
    [LAST_CSV_DATA_FILE10] = Fnamedata
    [CSV_DATA_FILE_DATE10] = FileDateTime(directory & Fnamedata)
    
    If wsExists("Text") Then
        [DEBRIEF_NOTES1].FormulaR1C1 = _
        "=IFERROR(IF(XLOOKUP(@ACT_NUM1,ACT_NUM3,ACT_NOTE3)=0,"""",XLOOKUP(@ACT_NUM1,ACT_NUM3,ACT_NOTE3)),"""")"
        [DEBRIEF_NOTES1].Copy: [DEBRIEF_NOTES1].PasteSpecial Paste:=xlPasteValues
        Application.CutCopyMode = False
    End If
    
    On Error Resume Next
    Sheets("Text").Delete
    
    Calculated_Data_Columns
    Application.EnableEvents = False

    Date_Column_Calculations
    Application.EnableEvents = False
    
    Range("A1").Select
    ActiveWindow.ScrollRow = 1
    ActiveWindow.ScrollColumn = 1

    On Error GoTo 0
    
    Application.EnableEvents = True
    
End Sub
Sub Get_Newest_CSV_Text_File()
    
    Application.DisplayAlerts = False
    Application.EnableEvents = False
    Application.ScreenUpdating = False

    UserId = (Environ$("Username"))
    
    If UCase([FILE_LOCATION11]) = "DEFAULT" Then
    directory = "C:\Users\<USER>\downloads\"
    Else
    directory = [FILE_LOCATION11]
    If Right(directory, 1) <> "\" Then directory = directory & "\"
    End If
    
    Fnametext = NewestTextFile(directory, "SQB - Text*.csv")
    
    If Fnametext = "" Then
Dim Msg, Style, Title, response
    Msg = "NO CSV CALL TEXT FILE WAS FOUND.  CONTINUE DATA LOAD WITH NO CALL TEXT?"
    Style = vbYesNo + vbQuestion + vbDefaultButton2
    Title = "CONTINUE WITHOUT CALL TEXT?"
    response = MsgBox(Msg, Style, Title)
        If response = vbNo Then
        Sheets("SETUP (START)").Select
        Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
        MsgBox ("If you would like to try again, please export the BI SQB call text report as type Data -> CSV Format to folder:" & Chr(10) & Chr(10) _
        & directory & Chr(10) & Chr(10) _
        & " Click the green ""LOAD DATA FROM SOURCE"" button at the top of the sheet when ready to retry."), vbInformation
        Application.StatusBar = ""
        Application.EnableEvents = True
        End
        Else
        Exit Sub
        End If
    End If
    
' ***********************SAVE LATEST FILE ONLY**********************
'Dim basename As String
'    On Error Resume Next
'    Do
'    UCase(Fnametext) = UCase(OldestFile(directory, "SQB - Text*(*).csv"))
'    If UCase(Fnametext) Like "*(?).CSV" Then
'        basename = Left(Fnametext, Len(Fnametext) - 7)
'        Kill directory & basename & ".csv"
'        Name directory & Fnametext As directory & basename & ".csv"
'    ElseIf UCase(Fnametext) Like "*(??).CSV" Then
'        basename = Left(Fnametext, Len(Fnametext) - 8)
'        Kill directory & basename & ".csv"
'        Name directory & Fnametext As directory & basename & ".csv"
'    End If
'    Loop Until Fnametext = ""
' ***********************END SAVE LATEST FILE ONLY******************

    [FILENAME_TEXT10] = directory & Fnametext
    
End Sub
Sub Select_SQB_CSV_Text_File()
    
    Application.DisplayAlerts = False
    Application.EnableEvents = False
    Application.ScreenUpdating = False

    UserId = (Environ$("Username"))
    
    If UCase([FILE_LOCATION11]) = "DEFAULT" Then
    directory = "C:\Users\<USER>\downloads\"
    Else
    directory = [FILE_LOCATION11]
    If Right(directory, 1) <> "\" Then directory = directory & "\"
    End If
    
    Fnametext = NewestTextFile(directory, "SQB - Text*.csv")

Dim fDialog As FileDialog, result As Integer
1:  Set fDialog = Application.FileDialog(msoFileDialogOpen)
     
'Optional: FileDialog properties
    fDialog.AllowMultiSelect = False
    fDialog.Title = "PLEASE SELECT THE BI SIDEKICK SQB CALL TEXT CSV FILE TO IMPORT."
    fDialog.InitialFileName = directory & Fnametext
     
'Optional: Add filters
    fDialog.Filters.Clear
    fDialog.Filters.Add "Text/CSV files", "*.csv"
'    fDialog.Filters.Add "Excel files", "*.xlsx"
'    fDialog.Filters.Add "All files", "*.*"
'    fDialog.Filters.Add "Excel files", "*.xlsx;*.xls;*.xlsm"
   
    If fDialog.Show = -1 Then
        Fnametext = Dir(fDialog.SelectedItems(1))
        GoTo 2
    End If
    GoTo 3
    
2:  If Not UCase(Fnametext) Like "SQB*.CSV" Or Not UCase(Fnametext) Like "*TEXT*.CSV" Then
        MsgBox ("The file selected for import does not appear to be a valid BI SideKick CSV call text file.  Valid BI SideKick call text files must begin with ""SQB"" and MUST contain the word ""Text"" in the filename." & Chr(10) & Chr(10) & _
        "Please select a valid BI SQB call text CSV file." & Chr(10) & Chr(10) & _
        "Current BI export folder and path =: " & directory), vbCritical
        GoTo 1
        Sheets("SETUP (START)").Select
        Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
        [TEMPLT_NAME_VER_11].Offset(7, 0).Select
        Application.StatusBar = ""
        Application.EnableEvents = True
        End
    End If
    
    [FILENAME_TEXT10] = directory & Fnametext
    
    Set fDialog = Nothing
    Application.StatusBar = ""
    Application.EnableEvents = True
    Exit Sub
    
Dim Msg, Style, Title, response
3:  Msg = "CONTINUE DATA LOAD WITH NO CALL TEXT?"
    Style = vbYesNoCancel + vbQuestion + vbDefaultButton1
    Title = "CONTINUE WITHOUT CALL TEXT?"
    response = MsgBox(Msg, Style, Title)
    If response = vbNo Then
    GoTo 1
    ElseIf response = vbCancel Then
        Sheets("SETUP (START)").Select
        Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
        [TEMPLT_NAME_VER_11].Offset(7, 0).Select
        Set fDialog = Nothing
        Application.StatusBar = ""
        Application.EnableEvents = True
        End
    Else
    Fnametext = ""
    [FILENAME_TEXT10].ClearContents
    Set fDialog = Nothing
    Application.StatusBar = ""
    Application.EnableEvents = True
    End If
    
End Sub
Sub Load_Call_Text_CSV()

    Application.DisplayStatusBar = True
    Application.StatusBar = "LOADING CALL TEXT"
    
    Application.DisplayAlerts = False
    Application.EnableEvents = False
    Application.ScreenUpdating = False

    On Error Resume Next
    Sheets("Text").Visible = True
    Sheets("Text").Delete
    On Error GoTo -1
    On Error GoTo 0
    
    If Fnametext = "" Then
    Application.StatusBar = ""
    Application.EnableEvents = True
    Exit Sub
    End If
    
    Sheets.Add After:=Sheets("Data")
    ActiveSheet.Name = "Text"
    
    UserId = (Environ$("Username"))
    
    If [FILE_LOCATION11] = "" Then [FILE_LOCATION11] = "DEFAULT"
    If UCase([FILE_LOCATION11]) = "DEFAULT" Then
        directory = "C:\Users\<USER>\downloads\"
    Else
    directory = [FILE_LOCATION11]
    If Right(directory, 1) <> "\" Then directory = directory & "\"
    End If
    
    If Not UCase(Dir([FILENAME_TEXT10])) Like "SQB *TEXT*.CSV" Then
        MsgBox ("The file selected for import does not appear to be a valid BI SideKick CSV call text file.  Valid BI SideKick Data files begin with ""SQB - Text""." & Chr(10) & Chr(10) & _
        "Make sure the BI SideKick Call History Report results are exported as ""Data -> CSV Format"" to the folder and path you specified at the top of the ""SETUP (START)"" sheet, then try again." & Chr(10) & Chr(10) & _
        "Current export folder and path =: " & directory), vbCritical
        Application.StatusBar = ""
        Application.EnableEvents = True
        Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
        End
    End If
    If UCase(Dir([FILENAME_TEXT10])) <> UCase(Dir(directory & Fnametext)) Then
        MsgBox ("Unable to load call text.  Cannot locate the CSV call text file selected.  Make sure the BI report results are exported as ""Data -> CSV Format"" to your: " & directory & " folder, then try again." _
        & "  If problems continue, make sure the BI Export File Location is correct, click the green ""LOAD DATA FROM SOURCE"" button at the top of the sheet, then select the files to import."), vbExclamation
        Sheets("SETUP (START)").Select
        Application.StatusBar = ""
        Application.EnableEvents = True
        Sheets("SETUP (START)").Shapes("Please Wait Processing").Visible = False
        End
    End If
    
    With Sheets("Text").ListObjects.Add(SourceType:=0, Source:= _
        "OLEDB;Provider=Microsoft.Mashup.OleDb.1;Data Source=$Workbook$;Location=SQBText;Extended Properties=""""" _
        , Destination:=Range("$A$1")).QueryTable
        .CommandType = xlCmdSql
        .CommandText = Array("SELECT * FROM [SQBText]")
        .RowNumbers = False
        .FillAdjacentFormulas = False
        .PreserveFormatting = False
        .RefreshOnFileOpen = False
        .BackgroundQuery = False
        .RefreshStyle = xlInsertDeleteCells
        .SavePassword = False
        .SaveData = False
        .AdjustColumnWidth = False
        .RefreshPeriod = 0
        .PreserveColumnInfo = False
        .ListObject.DisplayName = "SQBText"
        .Refresh BackgroundQuery:=False
    End With

'    ActiveSheet.ListObjects("SQBText").Unlist
'    If Sheets("CALL TEXT").Range("A1") = "" Then GoTo 2
    
    [LAST_CSV_TEXT_FILE10] = Fnametext
    [CSV_TEXT_FILE_DATE10] = FileDateTime(directory & Fnametext)
    
    Define_Names_Text
    
    Application.EnableEvents = False
    
    Sheets("Text").Range("A1").Activate
    ActiveWindow.ScrollRow = 1
    ActiveWindow.ScrollColumn = 1
'    Sheets("Text").Visible = xlSheetVeryHidden
    Application.StatusBar = ""
    
    Application.EnableEvents = True
    
End Sub
Sub Define_Names_Data()
    
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    
    Sheets("Data").Select
    Range(Range("A1"), Cells(1, Sheets("Data").Columns.Count).End(xlToLeft)).Name = "COLUMN_HEADINGS1"
    
    [COLUMN_HEADINGS1].CurrentRegion.Name = "DATA_LOOKUP1_1"
    
    If Sheets("Data").ListObjects("SQBCalls").AutoFilter Is Nothing Then
        Sheets("Data").ListObjects("SQBCalls").Range.AutoFilter
    Else
        Sheets("Data").ListObjects("SQBCalls").AutoFilter.ShowAllData
    End If

'    If Sheets("Data").AutoFilterMode Then
'        [DATA_LOOKUP1_1].AutoFilter
'        [DATA_LOOKUP1_1].AutoFilter
'    Else
'        [DATA_LOOKUP1_1].AutoFilter
'    End If
    
'    rowcnt = Sheets("Data").Cells(1, 1).CurrentRegion.Rows.Count - 1
    colnum4 = Sheets("Data").[COLUMN_HEADINGS1].Find("SR Number").Column
    rowcnt = Range(Sheets("Data").Cells(2, colnum4), Sheets("Data").Cells(1048576, colnum4).End(xlUp)).Rows.Count
    
    On Error GoTo 1
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Temp Data")
    GoTo 2
    
1:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 2
    Sheets("Data").Cells(1, Sheets("Data").Columns.Count).End(xlToLeft).Offset(0, 1) = "Temp Data"
    Range(Sheets("Data").Range("A1"), Sheets("Data").Cells(1, Sheets("Data").Columns.Count).End(xlToLeft)).Name = "COLUMN_HEADINGS1"
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Temp Data").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Temp Data").Offset(rowcnt, 0)).Name = "TEMP_DATA1"
    
2:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 3
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Site Num").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Site Num").Offset(rowcnt, 0)).Name = "SITE_NUM1"
    [SITE_NUM1].NumberFormat = "@"

3:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 4
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Legacy Account").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Legacy Account").Offset(rowcnt, 0)).Name = "LEGACY_SITE_NUM1"
    [LEGACY_SITE_NUM1].NumberFormat = "@"

4:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 5
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Device Group").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Device Group").Offset(rowcnt, 0)).Name = "DEVICE_GROUP1"
    
5:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 6
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("SR Status").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("SR Status").Offset(rowcnt, 0)).Name = "SR_STATUS1"
    
6:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 7
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Number").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Number").Offset(rowcnt, 0)).Name = "ACT_NUM1"
    
7:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 8
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Type").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Type").Offset(rowcnt, 0)).Name = "ACT_TYPE1"
    
8:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 9
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("SR Number").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("SR Number").Offset(rowcnt, 0)).Name = "SR_NUM1"
    
9:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 10
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Contact Date").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Contact Date").Offset(rowcnt, 0)).Name = "CONTACT_DATE1"
    [CONTACT_DATE1].NumberFormat = "mm/dd/yy h:mm;@"
    
10:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 11
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Effective Date").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Effective Date").Offset(rowcnt, 0)).Name = "EFFECT_DATE1"
    [EFFECT_DATE1].NumberFormat = "mm/dd/yy h:mm;@"
    
11: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 12
    With Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Facts Call Num").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Facts Call Num").Offset(rowcnt, 0))
        .Name = "ACT_FACTS_NUM1"
        .ColumnWidth = 12
    End With
    
12: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 13
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Arrival Date").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Arrival Date").Offset(rowcnt, 0)).Name = "ARRIVE_DATE1"
    [ARRIVE_DATE1].NumberFormat = "mm/dd/yy h:mm;@"
        
13: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 14
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Completed Date").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Completed Date").Offset(rowcnt, 0)).Name = "ACT_COMPLT_DATE1"
    [ACT_COMPLT_DATE1].NumberFormat = "mm/dd/yy h:mm;@"
    
14: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 15
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Status").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Status").Offset(rowcnt, 0)).Name = "ACT_STATUS1"

15: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 16
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("SR Description").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("SR Description").Offset(rowcnt, 0)).Name = "SR_DESCR1"
    
16: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 17
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Travel Hours").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Travel Hours").Offset(rowcnt, 0)).Name = "TRAVEL_HRS1"
    
17: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 18
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Wait Hours").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Wait Hours").Offset(rowcnt, 0)).Name = "WAIT_HRS1"
    
18: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 19
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Repair Hours").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Repair Hours").Offset(rowcnt, 0)).Name = "REPAIR_HRS1"
    
19: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 20
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("FCO Num").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("FCO Num").Offset(rowcnt, 0)).Name = "FCO_NUM1"
    
20: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 21
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice YN").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice YN").Offset(rowcnt, 0)).Name = "INV_DSC1"
    
21: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 22
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice Num").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice Num").Offset(rowcnt, 0)).Name = "INV_NUM1"
    
22: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 23
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice Labor").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice Labor").Offset(rowcnt, 0)).Name = "INV_LBR1"
    
23: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 24
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice Parts").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice Parts").Offset(rowcnt, 0)).Name = "INV_PARTS1"
    
24: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 25
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice Total").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Invoice Total").Offset(rowcnt, 0)).Name = "INV_TTL1"
    
25: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 26
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Base Call YN").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Base Call YN").Offset(rowcnt, 0)).Name = "BASE_CALL_FLAG1"
    
26: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 27
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Days to Next Call").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Days to Next Call").Offset(rowcnt, 0)).Name = "DAYS_NEXT_CALL1"
    
27: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 28
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("-Status-")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("-Status-").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("-Status-").Offset(rowcnt, 0)).Name = "CALL_STATUS1"

28: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 29
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("SR Type")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("SR Type").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("SR Type").Offset(rowcnt, 0)).Name = "SR_TYPE1"

29: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 30
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("SR Sub Type")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("SR Sub Type").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("SR Sub Type").Offset(rowcnt, 0)).Name = "SR_SUB_TYPE1"

30: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 31
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Holding Name")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Holding Name").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Holding Name").Offset(rowcnt, 0)).Name = "HOLDING_NAME1"
    
31: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 32
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Debrief Notes")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Debrief Notes").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Debrief Notes").Offset(rowcnt, 0)).Name = "DEBRIEF_NOTES1"
    
32: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 34
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Dispatch Ticket #")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Dispatch Ticket #").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Dispatch Ticket #").Offset(rowcnt, 0)).Name = "DISPATCH_TICKET1"
    
34: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 36
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Dispatch Status")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Dispatch Status").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Dispatch Status").Offset(rowcnt, 0)).Name = "DISPATCH_STATUS1"
    
36: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 38
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Year Date")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Year Date").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Year Date").Offset(rowcnt, 0)).Name = "YEAR_DATE1"
    [YEAR_DATE1].NumberFormat = "general"
    
38: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 40
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Month Num")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Month Num").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Month Num").Offset(rowcnt, 0)).Name = "MTH_NUM1"
    [MTH_NUM1].NumberFormat = "general"
    
40: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 42
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Month Name")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Month Name").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Month Name").Offset(rowcnt, 0)).Name = "MTH_NAME1"
    [MTH_NAME1].NumberFormat = "general"
    
42: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 44
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Week Num")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Week Num").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Week Num").Offset(rowcnt, 0)).Name = "WK_NUM1"
    [WK_NUM1].NumberFormat = "general"
    
44: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 46
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Day Num")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Day Num").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Day Num").Offset(rowcnt, 0)).Name = "DAY_NUM1"
    [DAY_NUM1].NumberFormat = "general"
    
46: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 48
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Week Ending Date")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Week Ending Date").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Week Ending Date").Offset(rowcnt, 0)).Name = "W_E_Date1"
    [W_E_Date1].NumberFormat = "mm/dd/yy"
    
48: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 49
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("SR Completion Date_Time")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("SR Completion Date_Time").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("SR Completion Date_Time").Offset(rowcnt, 0)).Name = "SR_COMPLT_DT1"
    [SR_COMPLT_DT1].NumberFormat = "mm/dd/yy h:mm;@"
    
49: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 50
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Install Date")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Install Date").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Install Date").Offset(rowcnt, 0)).Name = "INSTALL_DATE1"
    [INSTALL_DATE1].NumberFormat = "mm/dd/yy"
    
50: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 51
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Solutn Code1")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Solutn Code1").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Solutn Code1").Offset(rowcnt, 0)).Name = "SOL_CODE1_1"
    
51: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 56
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Model")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Model").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Model").Offset(rowcnt, 0)).Name = "MODEL1"
    
56: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 62
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Sol Code Module 1")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Sol Code Module 1").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Sol Code Module 1").Offset(rowcnt, 0)).Name = "SOL_MOD1_1"
    
62: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 63
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("SR Attachment Flg")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("SR Attachment Flg").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("SR Attachment Flg").Offset(rowcnt, 0)).Name = "SR_ATTACH_FLG1"
    
63: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 64
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Chronic Flg")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Chronic Flg").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Chronic Flg").Offset(rowcnt, 0)).Name = "CHRONIC_FLG1"
    
64: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 65
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Module Or Service")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Module Or Service").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Module Or Service").Offset(rowcnt, 0)).Name = "ASC1_MOD_SRV1"
    
65: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 66
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Specific Mod or Serv")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Specific Mod or Serv").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Specific Mod or Serv").Offset(rowcnt, 0)).Name = "ASC1_SPEC_MOD_SRV1"
    
66: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 67
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Sub Component")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Sub Component").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Sub Component").Offset(rowcnt, 0)).Name = "ASC1_SUB_COMP1"
    
67: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 68
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Reason For Service")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Reason For Service").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Reason For Service").Offset(rowcnt, 0)).Name = "ASC1_SERV_REASON1"
    
68: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 69
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Root Cause")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Root Cause").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Root Cause").Offset(rowcnt, 0)).Name = "ASC1_ROOT_CAUSE1"
    
69: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 70
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Detailed Cause")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Detailed Cause").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Detailed Cause").Offset(rowcnt, 0)).Name = "ASC1_DETAILED_CAUSE1"
    
70: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 71
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Billable Review")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Billable Review").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Billable Review").Offset(rowcnt, 0)).Name = "ASC1_BILL_REV_CDE1"
    
71: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 72
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Solution")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Solution").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Solution").Offset(rowcnt, 0)).Name = "ASC1_ACT_SOLUTION1"
    
72: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 73
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Solution")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Solution").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Solution").Offset(rowcnt, 0)).Name = "ASC1_ACT_SOLUTION1"
    
73: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 74
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Product Group")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Product Group").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Product Group").Offset(rowcnt, 0)).Name = "ASC1_PROD_GRP1"
    
74: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 75
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("PCID")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("PCID").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("PCID").Offset(rowcnt, 0)).Name = "PCID1"
    
75: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 76
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Location ID")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Location ID").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Location ID").Offset(rowcnt, 0)).Name = "LOC_ID1"
    
76: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 77
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Product Line")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Product Line").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Product Line").Offset(rowcnt, 0)).Name = "ASC1_PROD_LINE1"
    
77: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo Finish
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Base Call YN")

    On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 79
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Repeat")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Repeat").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Repeat").Offset(rowcnt, 0)).Name = "REPEAT1"
    
79: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 81
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Trvl Hrs (All Acts)")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Trvl Hrs (All Acts)").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Trvl Hrs (All Acts)").Offset(rowcnt, 0)).Name = "TTL_TRAVEL_ALL_ACTS1"
    [TTL_TRAVEL_ALL_ACTS1].NumberFormat = "0.0"

81: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 83
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Wait Hrs (All Acts)")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Wait Hrs (All Acts)").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Wait Hrs (All Acts)").Offset(rowcnt, 0)).Name = "TTL_WAIT_ALL_ACTS1"
    [TTL_WAIT_ALL_ACTS1].NumberFormat = "0.0"
    
83: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo Finish
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Repr Hrs (All Acts)")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Repr Hrs (All Acts)").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Repr Hrs (All Acts)").Offset(rowcnt, 0)).Name = "TTL_REPAIR_ALL_ACTS1"
    [TTL_REPAIR_ALL_ACTS1].NumberFormat = "0.0"

85: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 87
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Hrs (All Acts)")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Hrs (All Acts)").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("TTL Hrs (All Acts)").Offset(rowcnt, 0)).Name = "TTL_HRS_ALL_ACTS1"
    [TTL_HRS_ALL_ACTS1].NumberFormat = "0.0"

87: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo Finish
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Parent Instance Number")
    Range(Sheets("Data").[COLUMN_HEADINGS1].Find("Parent Instance Number").Offset(1, 0), Sheets("Data").[COLUMN_HEADINGS1].Find("Parent Instance Number").Offset(rowcnt, 0)).Name = "PARENT_IB_NUM1"

Finish:
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    Sheets("Data").[TEMP_DATA1].EntireColumn.Delete
    If WorksheetFunction.CountA(Range("SOL_CODE1_1")) = 0 Then [SOL_CODE1_1].EntireColumn.Delete
    If WorksheetFunction.CountA(Range("SOL_MOD1_1")) = 0 Then [SOL_MOD1_1].EntireColumn.Delete
    
    Range(Sheets("Data").Range("A1"), Sheets("Data").Cells(1, Sheets("Data").Columns.Count).End(xlToLeft)).Name = "COLUMN_HEADINGS1"
    colname = ""
    [COLUMN_HEADINGS1].CurrentRegion.Name = "DATA_LOOKUP1_1"
    On Error Resume Next
    Range([SR_NUM1], [SR_NUM1].SpecialCells(xlLastCell)).Name = "DATA_LOOKUP2_1"
    Range([ACT_NUM1], [ACT_NUM1].SpecialCells(xlLastCell)).Name = "DATA_LOOKUP3_1"
    Range([ACT_FACTS_NUM1], [ACT_FACTS_NUM1].SpecialCells(xlLastCell)).Name = "DATA_LOOKUP4_1"

    If Sheets("Data").ListObjects("SQBCalls").AutoFilter Is Nothing Then
        Sheets("Data").ListObjects("SQBCalls").Range.AutoFilter
    Else
        Sheets("Data").ListObjects("SQBCalls").AutoFilter.ShowAllData
    End If

'    If Sheets("Data").AutoFilterMode Then
'        [DATA_LOOKUP1_1].AutoFilter
'        [DATA_LOOKUP1_1].AutoFilter
'    Else
'        [DATA_LOOKUP1_1].AutoFilter
'    End If

    Application.EnableEvents = True
    
End Sub
Sub Calculated_Data_Columns()
    
    Application.ScreenUpdating = False
    Application.Calculation = xlManual
    Application.DisplayAlerts = False
    Application.EnableEvents = False

    Sheets("Data").Select
    
    [DATA_LOOKUP1_1].AutoFilter Field:=[DEVICE_GROUP1].Column, Criteria1:="TAB"
    If WorksheetFunction.Subtotal(3, [SR_NUM1]) > 0 Then
    [DISPATCH_TICKET1].FormulaR1C1 = "=SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SUBSTITUTE(SR_DESCR1,"". #"",""TICKET""),"" "","""")," & _
        """1-"",""1@@""),""TICKETS"",""TICKET""),""TKT"",""TICKET""),""INCIDENT"",""TICKET""),""TICKETNUMBER"",""TICKET""),"":"",""""),""-"",""""),""#"","""")"
    [DATA_LOOKUP1_1].AutoFilter Field:=[DEVICE_GROUP1].Column
    [DISPATCH_TICKET1].Copy
    [DISPATCH_STATUS1].PasteSpecial Paste:=xlPasteValues
    Application.CutCopyMode = False
    End If
    
    [DATA_LOOKUP1_1].AutoFilter Field:=[DEVICE_GROUP1].Column, Criteria1:="TAB"
    If WorksheetFunction.Subtotal(3, [SR_NUM1]) > 0 Then
    [DISPATCH_TICKET1].FormulaR1C1 = _
        "=IF(OR(LEFT(HOLDING_NAME1,11)=""WELLS FARGO"",ISERROR(SEARCH(""PRODUCT ID"",SR_DESCR1))),"""",TRIM(" & _
        "IFERROR(VALUE(MID(SR_DESCR1,SEARCH("". 4???????"",SR_DESCR1)+2,8)),IFERROR(VALUE(MID(SR_DESCR1,SEARCH("". 5???????"",SR_DESCR1)+2,8)),IFERROR(VALUE(MID(SR_DESCR1,SEARCH("". 6???????"",SR_DESCR1)+2,8))," & _
        "IFERROR(VALUE(MID(SR_DESCR1,SEARCH("". 7???????"",SR_DESCR1)+2,8)),IFERROR(VALUE(MID(SR_DESCR1,SEARCH("". 8???????"",SR_DESCR1)+2,8)),IFERROR(VALUE(MID(SR_DESCR1,SEARCH("". 9???????"",SR_DESCR1)+2,8))," & _
        "IFERROR(VALUE(MID(SR_DESCR1,SEARCH("". #?????????"",SR_DESCR1)+3,9)),"""")))))))))"
    End If
    
    [DATA_LOOKUP1_1].AutoFilter Field:=[DISPATCH_TICKET1].Column, Criteria1:=""
    If WorksheetFunction.Subtotal(3, [SR_NUM1]) > 0 Then
        [DISPATCH_TICKET1].FormulaR1C1 = _
            "=IF(OR(DEVICE_GROUP1<>""TAB"",LEFT(HOLDING_NAME1,11)=""WELLS FARGO"",ISNUMBER(SEARCH(""TICKET1@@???????????"",DISPATCH_STATUS1))),""""," & _
            "IFERROR(VALUE(MID(DISPATCH_STATUS1,SEARCH(""TICKET??????????"",DISPATCH_STATUS1)+6,10)),IFERROR(VALUE(MID(DISPATCH_STATUS1,SEARCH(""TICKET?????????"",DISPATCH_STATUS1)+6,9)),IFERROR(VALUE(MID(DISPATCH_STATUS1,SEARCH(""TICKET????????"",DISPATCH_STATUS1)+6,8))," & _
            "IFERROR(VALUE(MID(DISPATCH_STATUS1,SEARCH(""TICKET???????"",DISPATCH_STATUS1)+6,7)),IFERROR(VALUE(MID(DISPATCH_STATUS1,SEARCH(""TICKET??????"",DISPATCH_STATUS1)+6,6)),""""))))))"
    End If
    [DATA_LOOKUP1_1].AutoFilter Field:=[DEVICE_GROUP1].Column
    [DATA_LOOKUP1_1].AutoFilter Field:=[DISPATCH_TICKET1].Column
    [DISPATCH_TICKET1].Copy: [DISPATCH_TICKET1].PasteSpecial Paste:=xlPasteValues
    Application.CutCopyMode = False
    
    [DATA_LOOKUP1_1].AutoFilter Field:=[DEVICE_GROUP1].Column, Criteria1:="TAB"
    If WorksheetFunction.Subtotal(3, [SR_NUM1]) > 0 Then
    [DISPATCH_STATUS1].FormulaR1C1 = _
        "=IFERROR(IF(LEFT(HOLDING_NAME1,11)=""WELLS FARGO"",MID(SR_DESCR1,SEARCH(""PRODUCT ID"",SR_DESCR1)+18,60)," & _
        "IF(DISPATCH_TICKET1="""","""",TRIM(IFERROR(MID(SR_DESCR1,SEARCH(DISPATCH_TICKET1,SR_DESCR1)+LEN(DISPATCH_TICKET1),SEARCH("" ("",SR_DESCR1)-(SEARCH(DISPATCH_TICKET1,SR_DESCR1)+LEN(DISPATCH_TICKET1)))," & _
        "IFERROR(MID(SR_DESCR1,SEARCH(""FAULT:"",SR_DESCR1)+6,IFERROR(SEARCH(""TICKET"",SR_DESCR1,SEARCH(""FAULT:"",SR_DESCR1)+6),IFERROR(SEARCH(""SITE"",SR_DESCR1,SEARCH(""FAULT:"",SR_DESCR1)+6),LEN(SR_DESCR1)))-(SEARCH(""FAULT:"",SR_DESCR1)+6))," & _
        "IFERROR(MID(SR_DESCR1,SEARCH(""FAULT:"",SR_DESCR1)+6,SEARCH(""TICKET"",SR_DESCR1)-(SEARCH(""FAULT:"",SR_DESCR1)+6))," & _
        "IFERROR(MID(SR_DESCR1,SEARCH(""STATUS:"",SR_DESCR1)+7,LEN(SR_DESCR1)-(SEARCH(""STATUS:"",SR_DESCR1)+6))," & _
        "IFERROR(MID(SR_DESCR1,SEARCH(DISPATCH_TICKET1,SR_DESCR1)+LEN(DISPATCH_TICKET1),80),"""")))))))),"""")"
    End If
    [DATA_LOOKUP1_1].AutoFilter Field:=[DEVICE_GROUP1].Column
    [DISPATCH_STATUS1].Copy: [DISPATCH_STATUS1].PasteSpecial Paste:=xlPasteValues
    Application.CutCopyMode = False

    [DATA_LOOKUP1_1].AutoFilter Field:=[ACT_COMPLT_DATE1].Column, Criteria1:="<>"
    [DATA_LOOKUP1_1].AutoFilter Field:=[BASE_CALL_FLAG1].Column, Criteria1:="Y"
    
    [DAYS_NEXT_CALL1].FormulaR1C1 = _
        "=IF(AND(ACT_TYPE1<>""First Line"",ACT_TYPE1<>""Field Repair"",ACT_TYPE1<>""Second Line"",ACT_TYPE1<>""Part Order""),"""",IF((MINIFS(CONTACT_DATE1,PARENT_IB_NUM1,PARENT_IB_NUM1,ACT_TYPE1,ACT_TYPE1,BASE_CALL_FLAG1,""Y"",CONTACT_DATE1,"">""&CONTACT_DATE1,ACT_COMPLT_DATE1,""<>"")-ACT_COMPLT_DATE1)<0,"""",MINIFS(CONTACT_DATE1,PARENT_IB_NUM1,PARENT_IB_NUM1,ACT_TYPE1,ACT_TYPE1,BASE_CALL_FLAG1,""Y"",CONTACT_DATE1,"">""&CONTACT_DATE1,ACT_COMPLT_DATE1,""<>"")-ACT_COMPLT_DATE1))"

    [REPEAT1].FormulaR1C1 = "=IF(OR(INV_DSC1=""Y"",DAYS_NEXT_CALL1=""""),"""",IFERROR(IF(AND(ACT_TYPE1=""First Line"",BASE_CALL_FLAG1=""Y"",DAYS_NEXT_CALL1<4),""Y"",IF(AND(OR(ACT_TYPE1=""Field Repair"",ACT_TYPE1=""Second Line"",ACT_TYPE1=""Part Order""),BASE_CALL_FLAG1=""Y"",DAYS_NEXT_CALL1<8),""Y"","""")),""""))"
    
    [DATA_LOOKUP1_1].AutoFilter Field:=[ACT_COMPLT_DATE1].Column
    [DATA_LOOKUP1_1].AutoFilter Field:=[BASE_CALL_FLAG1].Column
    
    [DAYS_NEXT_CALL1] = [DAYS_NEXT_CALL1].Value
    [REPEAT1] = [REPEAT1].Value
    
    rowcnt = WorksheetFunction.CountIf([CALL_STATUS1], "Open") + WorksheetFunction.CountIf([CALL_STATUS1], "Canceled")
    lastrow = WorksheetFunction.CountA([SITE_NUM1])
    
    If rowcnt < lastrow And WorksheetFunction.CountA(Range("SR_COMPLT_DT1")) = 0 Then
    Get_SR_Complete_Date
    Application.EnableEvents = False
    
    On Error Resume Next
    Sheets("Data").Select
    [DATA_LOOKUP1_1].AutoFilter Field:=[ACT_COMPLT_DATE1].Column, Criteria1:="<>"
    If WorksheetFunction.Subtotal(3, [SITE_NUM1]) > 0 Then
    [SR_COMPLT_DT1].FormulaR1C1 = "=IFERROR(VLOOKUP(SR_NUM1,SR_COMPLT_DATE_LOOKUP,2,FALSE),"""")"
    End If
    [DATA_LOOKUP1_1].AutoFilter Field:=[ACT_COMPLT_DATE1].Column
    [SR_COMPLT_DT1] = [SR_COMPLT_DT1].Value
    Sheets("TempPivot").Delete
    End If

'    If [ACTINVDETAILS11] = "YES" Then
'    Activity_Invoice_Details
'    Application.EnableEvents = False
'    End If
    
    If Sheets("Data").ListObjects("SQBCalls").AutoFilter Is Nothing Then
        Sheets("Data").ListObjects("SQBCalls").Range.AutoFilter
    Else
        Sheets("Data").ListObjects("SQBCalls").AutoFilter.ShowAllData
    End If

'    If Sheets("Data").AutoFilterMode Then
'        [DATA_LOOKUP1_1].AutoFilter
'        [DATA_LOOKUP1_1].AutoFilter
'    Else
'        [DATA_LOOKUP1_1].AutoFilter
'    End If
   
    colname = ""

    Application.EnableEvents = True
    
End Sub
Sub Date_Column_Calculations()
    
    Application.ScreenUpdating = False
    Application.Calculation = xlManual
    Application.DisplayAlerts = False
    
    On Error Resume Next
    Application.EnableEvents = False
    [YEAR_DATE1].ClearContents
    [MTH_NUM1].ClearContents
    [MTH_NAME1].ClearContents
    [WK_NUM1].ClearContents
    [DAY_NUM1].ClearContents
    [W_E_Date1].ClearContents
    
    On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 1
    Select Case [WKDAY1_11]
    Case "Sunday", "Sun", "Su"
    [WKDAY2_11] = 1
    Case "Monday", "Mon", "Mo"
    [WKDAY2_11] = 2
    Case "Tuesday", "Tues", "Tue", "Tu"
    [WKDAY2_11] = 3
    Case "Wednesday", "Wed", "We"
    [WKDAY2_11] = 4
    Case "Thursday", "Thur", "Thu", "Th"
    [WKDAY2_11] = 5
    Case "Friday", "Fri", "Fr"
    [WKDAY2_11] = 6
    Case "Saturday", "Sat", "Sa"
    [WKDAY2_11] = 7
    End Select
    Select Case [DATE_TYPE11]
    Case "Contact"
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Contact Date")
    [YEAR_DATE1].FormulaR1C1 = "=IFERROR(IF(CONTACT_DATE1="""","""",YEAR(CONTACT_DATE1)),"""")"
    [YEAR_DATE1] = [YEAR_DATE1].Value
    [MTH_NUM1].FormulaR1C1 = "=IFERROR(IF(CONTACT_DATE1="""","""",MONTH(CONTACT_DATE1)),"""")"
    [MTH_NUM1] = [MTH_NUM1].Value
    [MTH_NAME1].FormulaR1C1 = "=IFERROR(IF(CONTACT_DATE1="""","""",TEXT(CONTACT_DATE1,""MMM"")),"""")"
    [MTH_NAME1] = [MTH_NAME1].Value
    [WK_NUM1].FormulaR1C1 = "=IFERROR(IF(CONTACT_DATE1="""","""",WEEKNUM(TRUNC(CONTACT_DATE1))),"""")"
    [WK_NUM1] = [WK_NUM1].Value
    [DAY_NUM1].FormulaR1C1 = "=IFERROR(IF(CONTACT_DATE1="""","""",DAY(CONTACT_DATE1)),"""")"
    [DAY_NUM1] = [DAY_NUM1].Value
    [W_E_Date1].FormulaR1C1 = _
        "=IFERROR(IF(CONTACT_DATE1="""","""",TRUNC(CONTACT_DATE1)+IF(WEEKDAY(CONTACT_DATE1)>WKDAY2_11,7+WKDAY2_11-WEEKDAY(CONTACT_DATE1),WKDAY2_11-WEEKDAY(CONTACT_DATE1))),"""")"
    [W_E_Date1] = [W_E_Date1].Value
    Case "Effective"
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Effective Date")
    [YEAR_DATE1].FormulaR1C1 = "=IFERROR(IF(EFFECT_DATE1="""","""",YEAR(EFFECT_DATE1)),"""")"
    [YEAR_DATE1] = [YEAR_DATE1].Value
    [MTH_NUM1].FormulaR1C1 = "=IFERROR(IF(EFFECT_DATE1="""","""",MONTH(EFFECT_DATE1)),"""")"
    [MTH_NUM1] = [MTH_NUM1].Value
    [MTH_NAME1].FormulaR1C1 = "=IFERROR(IF(EFFECT_DATE1="""","""",TEXT(EFFECT_DATE1,""MMM"")),"""")"
    [MTH_NAME1] = [MTH_NAME1].Value
    [WK_NUM1].FormulaR1C1 = "=IFERROR(IF(EFFECT_DATE1="""","""",WEEKNUM(TRUNC(EFFECT_DATE1))),"""")"
    [WK_NUM1] = [WK_NUM1].Value
    [DAY_NUM1].FormulaR1C1 = "=IFERROR(IF(EFFECT_DATE1="""","""",DAY(EFFECT_DATE1)),"""")"
    [DAY_NUM1] = [DAY_NUM1].Value
    [W_E_Date1].FormulaR1C1 = _
        "=IFERROR(IF(EFFECT_DATE1="""","""",TRUNC(EFFECT_DATE1)+IF(WEEKDAY(EFFECT_DATE1)>WKDAY2_11,7+WKDAY2_11-WEEKDAY(EFFECT_DATE1),WKDAY2_11-WEEKDAY(EFFECT_DATE1))),"""")"
    [W_E_Date1] = [W_E_Date1].Value
    Case "Activity Complete"
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("Activity Completed Date")
    [YEAR_DATE1].FormulaR1C1 = "=IFERROR(IF(ACT_COMPLT_DATE1="""","""",YEAR(ACT_COMPLT_DATE1)),"""")"
    [YEAR_DATE1] = [YEAR_DATE1].Value
    [MTH_NUM1].FormulaR1C1 = "=IFERROR(IF(ACT_COMPLT_DATE1="""","""",MONTH(ACT_COMPLT_DATE1)),"""")"
    [MTH_NUM1] = [MTH_NUM1].Value
    [MTH_NAME1].FormulaR1C1 = "=IFERROR(IF(ACT_COMPLT_DATE1="""","""",TEXT(ACT_COMPLT_DATE1,""MMM"")),"""")"
    [MTH_NAME1] = [MTH_NAME1].Value
    [WK_NUM1].FormulaR1C1 = "=IFERROR(IF(ACT_COMPLT_DATE1="""","""",WEEKNUM(TRUNC(ACT_COMPLT_DATE1))),"""")"
    [WK_NUM1] = [WK_NUM1].Value
    [DAY_NUM1].FormulaR1C1 = "=IFERROR(IF(ACT_COMPLT_DATE1="""","""",DAY(ACT_COMPLT_DATE1)),"""")"
    [DAY_NUM1] = [DAY_NUM1].Value
    [W_E_Date1].FormulaR1C1 = _
        "=IFERROR(IF(ACT_COMPLT_DATE1="""","""",TRUNC(ACT_COMPLT_DATE1)+IF(WEEKDAY(ACT_COMPLT_DATE1)>WKDAY2_11,7+WKDAY2_11-WEEKDAY(ACT_COMPLT_DATE1),WKDAY2_11-WEEKDAY(ACT_COMPLT_DATE1))),"""")"
    [W_E_Date1] = [W_E_Date1].Value
    Case "SR Complete"
    colname = Sheets("Data").[COLUMN_HEADINGS1].Find("SR Completion")
    [YEAR_DATE1].FormulaR1C1 = "=IFERROR(IF(SR_COMPLT_DT1="""","""",YEAR(SR_COMPLT_DT1)),"""")"
    [YEAR_DATE1] = [YEAR_DATE1].Value
    [MTH_NUM1].FormulaR1C1 = "=IFERROR(IF(SR_COMPLT_DT1="""","""",MONTH(SR_COMPLT_DT1)),"""")"
    [MTH_NUM1] = [MTH_NUM1].Value
    [MTH_NAME1].FormulaR1C1 = "=IFERROR(IF(SR_COMPLT_DT1="""","""",TEXT(SR_COMPLT_DT1,""MMM"")),"""")"
    [MTH_NAME1] = [MTH_NAME1].Value
    [WK_NUM1].FormulaR1C1 = "=IFERROR(IF(SR_COMPLT_DT1="""","""",WEEKNUM(TRUNC(SR_COMPLT_DT1))),"""")"
    [WK_NUM1] = [WK_NUM1].Value
    [DAY_NUM1].FormulaR1C1 = "=IFERROR(IF(SR_COMPLT_DT1="""","""",DAY(SR_COMPLT_DT1)),"""")"
    [DAY_NUM1] = [DAY_NUM1].Value
    [W_E_Date1].FormulaR1C1 = _
        "=IFERROR(IF(SR_COMPLT_DT1="""","""",TRUNC(SR_COMPLT_DT1)+IF(WEEKDAY(SR_COMPLT_DT1)>WKDAY2_11,7+WKDAY2_11-WEEKDAY(SR_COMPLT_DT1),WKDAY2_11-WEEKDAY(SR_COMPLT_DT1))),"""")"
    [W_E_Date1] = [W_E_Date1].Value
    End Select
    
1:  Application.EnableEvents = True
    
End Sub
Sub User_Defined_Service_Hrs()

    Application.ScreenUpdating = False
    
    Define_Names_Data
    Application.EnableEvents = False
    
'    If Sheets("Data").AutoFilterMode Then
'        [DATA_LOOKUP1_1].AutoFilter
'        [DATA_LOOKUP1_1].AutoFilter
'    Else
'        [DATA_LOOKUP1_1].AutoFilter
'    End If
   
    On Error GoTo 1
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Clear
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Add _
        Key:=Range("SQBCalls[SR Number]"), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Add _
        Key:=Range("SQBCalls[Activity Complete Date]"), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    With ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With

'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Clear
'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Add Key:= _
'        [SR_NUM1], SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Add Key:= _
'        [ACT_COMPLT_DATE1], SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
'    With ThisWorkbook.Worksheets("Data").AutoFilter.Sort
'        .Header = xlYes
'        .MatchCase = False
'        .Orientation = xlTopToBottom
'        .SortMethod = xlPinYin
'        .Apply
'    End With
    GoTo 2
    
1:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 2
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Clear
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Add _
        Key:=Range("SQBCalls[SR Number]"), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Add _
        Key:=Range("SQBCalls[Contact Date]"), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    With ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With
'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Clear
'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Add Key:= _
'        [SR_NUM1], SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Add Key:= _
'        [CONTACT_DATE1], SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
'    With ThisWorkbook.Worksheets("Data").AutoFilter.Sort
'        .Header = xlYes
'        .MatchCase = False
'        .Orientation = xlTopToBottom
'        .SortMethod = xlPinYin
'        .Apply
'    End With
    
2:  On Error GoTo -1
    On Error GoTo 0

    Application.EnableEvents = True
    
End Sub
Sub Activity_Invoice_Details()
    
    Application.EnableEvents = False
    
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Clear
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Add _
        Key:=Range("SQBCalls[SR Number]"), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Add _
        Key:=Range("SQBCalls[Invoice Number]"), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    With ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With
    ActiveWorkbook.Worksheets("Data").ListObjects("SQBCalls").Sort.SortFields.Clear

'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Clear
'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Add Key:= _
'        [SR_NUM1], SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Add Key:= _
'        [INV_NUM1], SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
'    With ThisWorkbook.Worksheets("Data").AutoFilter.Sort
'        .Header = xlYes
'        .MatchCase = False
'        .Orientation = xlTopToBottom
'        .SortMethod = xlPinYin
'        .Apply
'    End With
'    ThisWorkbook.Worksheets("Data").AutoFilter.Sort.SortFields.Clear
    
    [SR_NUM1].AutoFilter Field:=[INV_DSC1].Column, Criteria1:="Y"
    [SR_NUM1].AutoFilter Field:=[INV_NUM1].Column, Criteria1:=""
    If WorksheetFunction.Subtotal(3, [SR_NUM1]) > 0 Then
    Range([INV_NUM1], [INV_TTL1]).FormulaR1C1 = _
        "=IFERROR(IF(VLOOKUP(SR_NUM1,DATA_LOOKUP2_1,MATCH(R1C,COLUMN_HEADINGS1,0)+1-MATCH(""SR Number"",COLUMN_HEADINGS1,0),FALSE)=0,"""",VLOOKUP(SR_NUM1,DATA_LOOKUP2_1,MATCH(R1C,COLUMN_HEADINGS1,0)+1-MATCH(""SR Number"",COLUMN_HEADINGS1,0),FALSE)),"""")"
    End If
    [SR_NUM1].AutoFilter Field:=[INV_DSC1].Column
    [SR_NUM1].AutoFilter Field:=[INV_NUM1].Column
    Range([INV_NUM1], [INV_TTL1]) = Range([INV_NUM1], [INV_TTL1]).Value

1:  On Error GoTo -1
    On Error GoTo 0
    Application.EnableEvents = True
    
End Sub
Sub Get_SR_Complete_Date()
    
    Application.ScreenUpdating = False
    Application.EnableEvents = False
    Application.DisplayAlerts = False
    
    On Error Resume Next
    
    Sheets("TempPivot").Delete
    
    Sheets.Add After:=ActiveSheet
    ActiveSheet.Name = "TempPivot"
    
    lastrow = Sheets("Data").Cells(1, 1).CurrentRegion.Rows.Count
    lastcol = Sheets("Data").Cells(1, 1).CurrentRegion.Columns.Count

    ThisWorkbook.PivotCaches.Create(SourceType:=xlDatabase, SourceData:= _
        "DATA!R1C1:R" & lastrow & "C" & lastcol).CreatePivotTable _
        TableDestination:="TempPivot!R3C1", TableName:="PivotTableData"
    
    lastrow = 0
    lastcol = 0
    
    With Sheets("TempPivot").PivotTables("PivotTableData").PivotFields("SR Number")
        .Orientation = xlRowField
        .Position = 1
    End With
    
    Sheets("TempPivot").PivotTables("PivotTableData").AddDataField Sheets("TempPivot").PivotTables( _
        "PivotTableData").PivotFields("Activity Completed Date"), _
        "Max of Activity Completed Date", xlMax
    
    With Sheets("TempPivot").PivotTables("PivotTableData").PivotFields("-Status-")
        .Orientation = xlPageField
        .Position = 1
    End With
    
    With Sheets("TempPivot").PivotTables("PivotTableData").PivotFields("Activity Status")
        .Orientation = xlPageField
        .Position = 1
    End With
    
    With Sheets("TempPivot").PivotTables("PivotTableData").PivotFields("-Status-")
        .PivotItems("Cancelled").Visible = False
        .PivotItems("Canceled").Visible = False
        .PivotItems("Open").Visible = False
    End With
    
    With Sheets("TempPivot").PivotTables("PivotTableData").PivotFields("Activity Status")
        .PivotItems("Arrived").Visible = False
        .PivotItems("Assigned").Visible = False
        .PivotItems("Cancelled").Visible = False
        .PivotItems("Enroute").Visible = False
        .PivotItems("Open").Visible = False
        .PivotItems("Unassigned").Visible = False
        .PivotItems("Waiting").Visible = False
        .PivotItems("Working").Visible = False
    End With
    
    Sheets("TempPivot").PivotTables("PivotTableData").SaveData = False
    
    Sheets("TempPivot").Range("A4").CurrentRegion.Name = "SR_COMPLT_DATE_LOOKUP"
    
    On Error GoTo 0
    
    Application.EnableEvents = True
    
End Sub
Sub Define_Names_Report()
    
    Application.EnableEvents = False
    
    Sheets("Report").Select
    Range(Range("A1"), Cells(1, Sheets("Report").Columns.Count).End(xlToLeft)).Name = "COLUMN_HEADINGS2"
    If Sheets("Report").AutoFilterMode Then
        [COLUMN_HEADINGS2].EntireColumn.AutoFilter
        [COLUMN_HEADINGS2].EntireColumn.AutoFilter
    Else
        [COLUMN_HEADINGS2].EntireColumn.AutoFilter
    End If
    
'    rowcnt = Sheets("Report").Cells(1, 1).CurrentRegion.Rows.Count - 1
    On Error Resume Next
    colnum4 = Sheets("Report").[COLUMN_HEADINGS2].Find("SR Number").Column
    colnum4 = Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Number").Column
    colnum4 = Sheets("Report").[COLUMN_HEADINGS2].Find("Site Number").Column
    rowcnt = Range(Sheets("Report").Cells(2, colnum4), Sheets("Report").Cells(1048576, colnum4).End(xlUp)).Rows.Count
    
    On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 1
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Site Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Site Num").Offset(rowcnt, 0))
        .Name = "SITE_NUM2"
        .NumberFormat = "@"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
1:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 2
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Legacy Account").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Legacy Account").Offset(rowcnt, 0))
        .Name = "LEGACY_SITE_NUM2"
        .NumberFormat = "@"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With

2:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 3
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Facts Call Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Facts Call Num").Offset(rowcnt, 0))
        .Name = "ACT_FACTS_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 11
    End With
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    With Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Facts Call Num")
        .Comment.Delete
        .AddComment
        .Comment.Text Text:="CLICK ON AN ACTIVITY FACTS CALL NUMBER TO VIEW THE ACTIVITY DETAILS, NOTES, AND PARTS WITHIN A POP UP WINDOW."
        .Comment.Shape.Height = 60
        .Comment.Shape.Width = 150
    End With

3:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 4
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Number").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Number").Offset(rowcnt, 0))
        .Name = "SR_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 11
    End With
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    With Sheets("Report").[COLUMN_HEADINGS2].Find("SR Number")
        .Comment.Delete
        .AddComment
        .Comment.Text Text:="CLICK ON AN SR NUMBER TO VIEW THE CALL DETAILS, NOTES, AND PARTS WITHIN A POP UP WINDOW."
        .Comment.Shape.Height = 60
        .Comment.Shape.Width = 150
    End With

4:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 5
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Number").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Number").Offset(rowcnt, 0))
        .Name = "ACT_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 14
    End With
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    With Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Number")
        .Comment.Delete
        .AddComment
        .Comment.Text Text:="CLICK ON AN ACTIVITY NUMBER TO VIEW THE ACTIVITY DETAILS, NOTES, AND PARTS WITHIN A POP UP WINDOW."
        .Comment.Shape.Height = 60
        .Comment.Shape.Width = 150
    End With

5:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 6
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Type").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Type").Offset(rowcnt, 0))
        .Name = "ACT_TYPE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
6:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 7
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Status").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Status").Offset(rowcnt, 0))
        .Name = "ACT_STATUS2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 14
    End With
    
7:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 8
    Sheets("Report").[COLUMN_HEADINGS2].Find("Device Group").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Device Group").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Device Group").Offset(rowcnt, 0))
        .Name = "DEVICE_GROUP2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 5
    End With
    
8:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 9
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Model").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Model").Offset(rowcnt, 0))
        .Name = "MODEL2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 9
    End With
    
9:  On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 10
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Sub Type").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Sub Type").Offset(rowcnt, 0))
        .Name = "ACT_SUB_TYPE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
10: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 11
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Trouble Code").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Trouble Code").Offset(rowcnt, 0))
        .Name = "TBL_DESCR2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
11: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 12
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Incomplete Reason").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Incomplete Reason").Offset(rowcnt, 0))
        .Name = "ACT_INC_REASON2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
12: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 13
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Action Requested").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Action Requested").Offset(rowcnt, 0))
        .Name = "ACTION_REQ2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
13: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 14
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("FCO Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("FCO Num").Offset(rowcnt, 0))
        .Name = "FCO_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 6
    End With
    
14: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 15
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Contact Date").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Contact Date").Offset(rowcnt, 0))
        .Name = "CONTACT_DATE2"
        .NumberFormat = "mm/dd/yy h:mm;@"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 15
    End With
    
15: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 16
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Effective Date").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Effective Date").Offset(rowcnt, 0))
        .Name = "EFFECT_DATE2"
        .NumberFormat = "mm/dd/yy h:mm;@"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 15
    End With
    
16: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 17
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Arrival Date").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Arrival Date").Offset(rowcnt, 0))
        .Name = "ARRIVE_DATE2"
        .NumberFormat = "mm/dd/yy h:mm;@"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 15
    End With
    
17: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 18
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Completed Date").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Completed Date").Offset(rowcnt, 0))
        .Name = "ACT_COMPLT_DATE2"
        .NumberFormat = "mm/dd/yy h:mm;@"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 15
    End With
    
18: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 19
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Activity CLICK ETA").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Activity CLICK ETA").Offset(rowcnt, 0))
        .Name = "ACT_ETA2"
        .NumberFormat = "mm/dd/yy h:mm;@"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 15
    End With
    
19: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 20
    Sheets("Report").[COLUMN_HEADINGS2].Find("Resp Time Con Arr Contract").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Resp Time Con Arr Contract").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Resp Time Con Arr Contract").Offset(rowcnt, 0))
        .Name = "RESP_SYS_CONTACT_CONTR2"
        .NumberFormat = "0.0"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 6
    End With
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    With Sheets("Report").[COLUMN_HEADINGS2].Find("Resp Time Con Arr Contract")
        .Comment.Delete
        .AddComment
        .Comment.Text Text:="RESPONSE TIMES WILL BE HIGHLIGHTED IN RED IF > THE RESPONSE THRESHOLD SET IN THE ""SLA DATA"" SETUPS."
        .Comment.Shape.Height = 60
        .Comment.Shape.Width = 150
    End With

20: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 21
    Sheets("Report").[COLUMN_HEADINGS2].Find("Resp Time Eff Arr Contract").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Resp Time Eff Arr Contract").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Resp Time Eff Arr Contract").Offset(rowcnt, 0))
        .Name = "RESP_SYS_EFFECT_CONTR2"
        .NumberFormat = "0.0"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 6
    End With
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    With Sheets("Report").[COLUMN_HEADINGS2].Find("Resp Time Eff Arr Contract")
        .Comment.Delete
        .AddComment
        .Comment.Text Text:="RESPONSE TIMES WILL BE HIGHLIGHTED IN RED IF > THE RESPONSE THRESHOLD SET IN THE ""SLA DATA"" SETUPS."
        .Comment.Shape.Height = 60
        .Comment.Shape.Width = 150
    End With
    
21: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 22
    Sheets("Report").[COLUMN_HEADINGS2].Find("Wait Hours").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Wait Hours").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Wait Hours").Offset(rowcnt, 0))
        .Name = "WAIT_HRS2"
        .NumberFormat = "0.00"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 5
    End With
    
22: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 23
    Sheets("Report").[COLUMN_HEADINGS2].Find("Repair Hours").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Repair Hours").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Repair Hours").Offset(rowcnt, 0))
        .Name = "REPAIR_HRS2"
        .NumberFormat = "0.00"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 5
    End With
    
23: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 24
    Sheets("Report").[COLUMN_HEADINGS2].Find("Travel Hours").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Travel Hours").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Travel Hours").Offset(rowcnt, 0))
        .Name = "TRAVEL_HRS2"
        .NumberFormat = "0.00"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 5
    End With
    
24: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 25
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Tech Team Name").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Tech Team Name").Offset(rowcnt, 0))
        .Name = "TECH_TEAM_NAME2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
25: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 26
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Tech Team Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Tech Team Num").Offset(rowcnt, 0))
        .Name = "TECH_TEAM_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
26: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 27
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Status Byte").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Status Byte").Offset(rowcnt, 0))
        .Name = "STATUS_BYTE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
27: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 28
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Urgency").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Urgency").Offset(rowcnt, 0))
        .Name = "URG_CDE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
28: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 29
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Priority").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Priority").Offset(rowcnt, 0))
        .Name = "PRIORITY2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
29: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 30
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Base Call YN").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Base Call YN").Offset(rowcnt, 0))
        .Name = "BASE_CALL_FLAG2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 5
    End With
    
30: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 31
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Area Desc").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Area Desc").Offset(rowcnt, 0))
        .Name = "AREA2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
31: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 32
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Region Desc").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Region Desc").Offset(rowcnt, 0))
        .Name = "REGION2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
32: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 33
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Team Desc").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Team Desc").Offset(rowcnt, 0))
        .Name = "TEAM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
33: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 34
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Branch Desc").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Branch Desc").Offset(rowcnt, 0))
        .Name = "BRANCH2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
34: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 35
    Sheets("Report").[COLUMN_HEADINGS2].Find("Days To Next Call").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Days To Next Call").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Days To Next Call").Offset(rowcnt, 0))
        .Name = "DAYS_NEXT_CALL2"
        .NumberFormat = "0.0"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 4
    End With
    
35: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 36
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Special PO").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Special PO").Offset(rowcnt, 0))
        .Name = "SPCL_PO2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
36: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 37
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Quoted Amount").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Quoted Amount").Offset(rowcnt, 0))
        .Name = "QUOTE_AMT2"
        .NumberFormat = "$0.00"
        .HorizontalAlignment = xlGeneral
        .ColumnWidth = 7
    End With
    
37: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 38
    Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice YN").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice YN").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice YN").Offset(rowcnt, 0))
        .Name = "INV_DSC2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 4
    End With
    
38: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 39
    Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Num").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Num").Offset(rowcnt, 0))
        .Name = "INV_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 9
    End With
    
39: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 40
    Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Labor").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Labor").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Labor").Offset(rowcnt, 0))
        .Name = "INV_LABOR2"
        .NumberFormat = "$0.00"
        .HorizontalAlignment = xlGeneral
        .ColumnWidth = 7
    End With
    
40: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 41
    Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Parts").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Parts").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Parts").Offset(rowcnt, 0))
        .Name = "INV_PARTS2"
        .NumberFormat = "$0.00"
        .HorizontalAlignment = xlGeneral
        .ColumnWidth = 7
    End With
    
41: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 42
    Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Total").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Total").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Total").Offset(rowcnt, 0))
        .Name = "INV_TOTAL2"
        .NumberFormat = "$0.00"
        .HorizontalAlignment = xlGeneral
        .ColumnWidth = 7
    End With
    
42: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 43
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Date").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Invoice Date").Offset(rowcnt, 0))
        .Name = "INVOICE_DATE2"
        .NumberFormat = "mm/dd/yy"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 8
    End With
    
43: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 44
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Install Date").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Install Date").Offset(rowcnt, 0))
        .Name = "INSTALL_DATE2"
        .NumberFormat = "mm/dd/yy"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 10
    End With
    
44: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 45
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Description").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Description").Offset(rowcnt, 0))
        .Name = "SR_DESCR2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 80
    End With
    
45: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 46
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Holding Name").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Holding Name").Offset(rowcnt, 0))
        .Name = "HOLDING_NAME2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 30
    End With
    
46: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 47
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Parent Name").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Parent Name").Offset(rowcnt, 0))
        .Name = "PARENT_NAME2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 30
    End With
    
47: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 48
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Product ID").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Product ID").Offset(rowcnt, 0))
        .Name = "PRODUCT_ID2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 11
    End With
    
48: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 49
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Location").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Location").Offset(rowcnt, 0))
        .Name = "LOCATION2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 28
    End With
    
49: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 50
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Address").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Address").Offset(rowcnt, 0))
        .Name = "ADDRESS2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 28
    End With
    
50: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 51
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("City").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("City").Offset(rowcnt, 0))
        .Name = "CITY2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
51: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 52
    Sheets("Report").[COLUMN_HEADINGS2].Find("State").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("State").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("State").Offset(rowcnt, 0))
        .Name = "STATE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 4
    End With
    
52: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 53
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Zip Code").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Zip Code").Offset(rowcnt, 0))
        .Name = "ZIP_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 7
    End With
    
53: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 54
    Sheets("Report").[COLUMN_HEADINGS2].Find("Country").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Country").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Country").Offset(rowcnt, 0))
        .Name = "COUNTRY2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 4
    End With
    
54: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 55
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Serial Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Serial Num").Offset(rowcnt, 0))
        .Name = "SERIAL_NUM2"
        .NumberFormat = "00"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 15
    End With
    
55: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 56
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Install Base Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Install Base Num").Offset(rowcnt, 0))
        .Name = "IB_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 7
    End With
    
56: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 57
    Sheets("Report").[COLUMN_HEADINGS2].Find("Coverage Type").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Coverage Type").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Coverage Type").Offset(rowcnt, 0))
        .Name = "CVRG_TYPE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
57: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 58
    Sheets("Report").[COLUMN_HEADINGS2].Find("Coverage Hours").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Coverage Hours").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Coverage Hours").Offset(rowcnt, 0))
        .Name = "CVG_HRS2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 4
    End With
    
58: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 59
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Default Service Branch").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Default Service Branch").Offset(rowcnt, 0))
        .Name = "SITE_BRANCH2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 8
    End With
    
59: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 60
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Technician Name").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Technician Name").Offset(rowcnt, 0))
        .Name = "TECH2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
60: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 61
    Sheets("Report").[COLUMN_HEADINGS2].Find("Technician Num").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Technician Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Technician Num").Offset(rowcnt, 0))
        .Name = "TECH_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 7
    End With
    
61: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 62
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Technician Title").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Technician Title").Offset(rowcnt, 0))
        .Name = "TECH_TITLE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
62: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 63
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Contact Name").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Contact Name").Offset(rowcnt, 0))
        .Name = "CONTACT_NAME2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
63: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 64
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 1").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 1").Offset(rowcnt, 0))
        .Name = "SOL_MOD1_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
64: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 65
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 2").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 2").Offset(rowcnt, 0))
        .Name = "SOL_MOD2_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
65: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 66
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 3").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 3").Offset(rowcnt, 0))
        .Name = "SOL_MOD3_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
66: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 67
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 4").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 4").Offset(rowcnt, 0))
        .Name = "SOL_MOD4_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
67: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 68
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 5").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 5").Offset(rowcnt, 0))
        .Name = "SOL_MOD5_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
68: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 69
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 6").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Sol Code Module 6").Offset(rowcnt, 0))
        .Name = "SOL_MOD6_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
69: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 70
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code1").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code1").Offset(rowcnt, 0))
        .Name = "SOL_CODE1_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
70: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 71
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code2").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code2").Offset(rowcnt, 0))
        .Name = "SOL_CODE2_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
71: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 72
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code3").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code3").Offset(rowcnt, 0))
        .Name = "SOL_CODE3_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
72: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 73
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code4").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code4").Offset(rowcnt, 0))
        .Name = "SOL_CODE4_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
73: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 74
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code5").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code5").Offset(rowcnt, 0))
        .Name = "SOL_CODE5_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
74: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 75
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code6").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Solutn Code6").Offset(rowcnt, 0))
        .Name = "SOL_CODE6_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 23
    End With
    
75: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 76
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Contact Name").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Contact Name").Offset(rowcnt, 0))
        .Name = "CONTACT_NAME2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
76: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 77
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Contact Phone Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Contact Phone Num").Offset(rowcnt, 0))
        .Name = "CONTACT_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
77: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 78
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Customer Problem Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Customer Problem Num").Offset(rowcnt, 0))
        .Name = "CUST_PROB_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
78: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 79
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Parent Instance Number").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Parent Instance Number").Offset(rowcnt, 0))
        .Name = "PARENT_IB_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 9
    End With
    
79: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 80
    Sheets("Report").[COLUMN_HEADINGS2].Find("Remote Resolve Reason").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Remote Resolve Reason").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Remote Resolve Reason").Offset(rowcnt, 0))
        .Name = "OV_REASON_CDE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 4.14
    End With
    
80: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 81
    Sheets("Report").[COLUMN_HEADINGS2].Find("SR Source").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Source").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Source").Offset(rowcnt, 0))
        .Name = "SR_SOURCE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
81: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 82
    Sheets("Report").[COLUMN_HEADINGS2].Find("SR Priority").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Priority").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Priority").Offset(rowcnt, 0))
        .Name = "SR_PRIORITY2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
82: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 83
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("County").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("County").Offset(rowcnt, 0))
        .Name = "COUNTY_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 16
    End With
    
83: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 84
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("ASC1").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("ASC1").Offset(rowcnt, 0))
        .Name = "ASC1_2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 18
    End With
    
84: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 85
    Sheets("Report").[COLUMN_HEADINGS2].Find("SR Attachment Flg").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Attachment Flg").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Attachment Flg").Offset(rowcnt, 0))
        .Name = "SR_ATTACH_FLG2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 4
    End With
    
85: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 86
    Sheets("Report").[COLUMN_HEADINGS2].Find("Chronic Flg").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Chronic Flg").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Chronic Flg").Offset(rowcnt, 0))
        .Name = "CHRONIC_FLG2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 4
    End With
    
86: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 87
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Module Or Service").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Module Or Service").Offset(rowcnt, 0))
        .Name = "ASC1_MOD_SRV2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 9
    End With
    
87: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 88
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Specific Module Or Serv").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Specific Module Or Serv").Offset(rowcnt, 0))
        .Name = "ASC1_SPEC_MOD_SRV2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 9
    End With
    
88: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 89
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Sub Component").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Sub Component").Offset(rowcnt, 0))
        .Name = "ASC1_SUB_COMP2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
89: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Status").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Status").Offset(rowcnt, 0))
        .Name = "SR_STATUS2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
90: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 91
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Root Cause").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Root Cause").Offset(rowcnt, 0))
        .Name = "ASC1_ROOT_CAUSE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 9
    End With
    
91: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 92
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Detailed Cause").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Detailed Cause").Offset(rowcnt, 0))
        .Name = "ASC1_DETAILED_CAUSE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 9
    End With
    
92: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 93
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Solution").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Activity Solution").Offset(rowcnt, 0))
        .Name = "ASC1_ACT_SOLUTION2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 9
    End With
    
93: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 94
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Product Group").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Product Group").Offset(rowcnt, 0))
        .Name = "ASC1_PROD_GRP2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 9
    End With
    
94: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 95
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Type").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Type").Offset(rowcnt, 0))
        .Name = "SR_TYPE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
95: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 96
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Sub Type").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Sub Type").Offset(rowcnt, 0))
        .Name = "SR_SUB_TYPE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 12
    End With
    
96: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 97
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("PCID").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("PCID").Offset(rowcnt, 0))
        .Name = "PCID2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 9
    End With
    
97: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 98
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Location ID").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Location ID").Offset(rowcnt, 0))
        .Name = "LOC_ID2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    
98: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 99
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("-Status-").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("-Status-").Offset(rowcnt, 0))
        .Name = "CALL_STATUS2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 10
    End With
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    With Sheets("Report").[COLUMN_HEADINGS2].Find("-Status-")
        .Comment.Delete
        .AddComment
        .Comment.Text Text:="-STATUS- = STATUS OF THE SERVICE REQUEST, NOT THE ACTIVITY. COMPLETE ONLY IF NO COUNTED ACTIVITIES ARE OPEN."
        .Comment.Shape.Height = 60
        .Comment.Shape.Width = 150
    End With
    
99: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 100
    Sheets("Report").[COLUMN_HEADINGS2].Find("Repeat").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Repeat").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Repeat").Offset(rowcnt, 0))
        .Name = "REPEAT2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 4
    End With
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    With Sheets("Report").[COLUMN_HEADINGS2].Find("Repeat")
        .Comment.Delete
        .AddComment
        .Comment.Text Text:="REPEAT = ANOTHER BASE PERFORMANCE CALL ON THE SAME SERVICE ITEM WITHIN 7 DAYS FOR SECOND LINE OR WITHIN 3 DAYS FOR FIRST LINE."
        .Comment.Shape.Height = 60
        .Comment.Shape.Width = 150
    End With
    
100: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 101
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Dispatch Ticket #").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Dispatch Ticket #").Offset(rowcnt, 0))
        .Name = "DISPATCH_TICKET2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 10
    End With
    
101: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 102
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Dispatch Status").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Dispatch Status").Offset(rowcnt, 0))
        .Name = "DISPATCH_STATUS2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 30
    End With
    
102: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 103
    Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Trvl Hrs (All Acts)").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Trvl Hrs (All Acts)").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Trvl Hrs (All Acts)").Offset(rowcnt, 0))
        .Name = "TTL_TRAVEL_ALL_ACTS2"
        .NumberFormat = "0.00"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 5
    End With
    
103: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 104
    Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Wait Hrs (All Acts)").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Wait Hrs (All Acts)").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Wait Hrs (All Acts)").Offset(rowcnt, 0))
        .Name = "TTL_WAIT_ALL_ACTS2"
        .NumberFormat = "0.00"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 5
    End With
    
104: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 105
    Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Repr Hrs (All Acts)").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Repr Hrs (All Acts)").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Repr Hrs (All Acts)").Offset(rowcnt, 0))
        .Name = "TTL_REPAIR_ALL_ACTS2"
        .NumberFormat = "0.00"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 5
    End With
    
105: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 106
    Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Hrs (All Acts)").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Hrs (All Acts)").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("TTL Hrs (All Acts)").Offset(rowcnt, 0))
        .Name = "TTL_HRS_ALL_ACTS2"
        .NumberFormat = "0.00"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 5
    End With
    
106: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 107
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Contact Name").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Contact Name").Offset(rowcnt, 0))
        .Name = "SR_CONT_NAME2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 12
    End With
    
107: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 108
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Contact Phone Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Contact Phone Num").Offset(rowcnt, 0))
        .Name = "SR_CONT_PHONE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 10
    End With
    
108: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 109
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("SR Completion Date_Time").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("SR Completion Date_Time").Offset(rowcnt, 0))
        .Name = "SR_COMPLT_DT2"
        .NumberFormat = "mm/dd/yy h:mm;@"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 15
    End With
    On Error GoTo -1
    On Error GoTo 0
    On Error Resume Next
    With Sheets("Report").[COLUMN_HEADINGS2].Find("SR Completion Date_Time")
        .Comment.Delete
        .AddComment
        .Comment.Text Text:="LATEST NON-CANCELED ACTIVITY COMPLETE DATE AND TIME FOR COMPLETED, NON-CANCELLED SRs."
        .Comment.Shape.Height = 60
        .Comment.Shape.Width = 200
    End With
    
109: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 110
    If WorksheetFunction.CountA(Range("DEBRIEF_NOTES1")) = 0 Then
'        If Not wsExists("Text") Then
        Sheets("Report").[COLUMN_HEADINGS2].Find("Debrief Notes").EntireColumn.Delete
        GoTo 110
'        End If
    End If
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Debrief Notes").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Debrief Notes").Offset(rowcnt, 0))
        .Name = "DEBRIEF_NOTES2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlLeft
        .ColumnWidth = 30
    End With
    
110: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 111
    Sheets("Report").[COLUMN_HEADINGS2].Find("Year Date").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Year Date").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Year Date").Offset(rowcnt, 0))
        .Name = "YEAR_DATE2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 4
    End With
    
111: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 112
    Sheets("Report").[COLUMN_HEADINGS2].Find("Month Num").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Month Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Month Num").Offset(rowcnt, 0))
        .Name = "MTH_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 3
    End With
    
112: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 113
    Sheets("Report").[COLUMN_HEADINGS2].Find("Month Name").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Month Name").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Month Name").Offset(rowcnt, 0))
        .Name = "MTH_NAME2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 3
    End With
    
113: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 114
    Sheets("Report").[COLUMN_HEADINGS2].Find("Week Num").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Week Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Week Num").Offset(rowcnt, 0))
        .Name = "WK_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 3
    End With
    
114: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 115
    Sheets("Report").[COLUMN_HEADINGS2].Find("Day Num").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Day Num").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Day Num").Offset(rowcnt, 0))
        .Name = "DAY_NUM2"
        .NumberFormat = "General"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 3
    End With
    
115: On Error GoTo -1
    On Error GoTo 0
    On Error GoTo 116
    Sheets("Report").[COLUMN_HEADINGS2].Find("Week Ending Date").Orientation = 90
    With Range(Sheets("Report").[COLUMN_HEADINGS2].Find("Week Ending Date").Offset(1, 0), Sheets("Report").[COLUMN_HEADINGS2].Find("Week Ending Date").Offset(rowcnt, 0))
        .Name = "W_E_DATE2"
        .NumberFormat = "mm/dd/yy"
        .HorizontalAlignment = xlCenter
        .ColumnWidth = 6
    End With
    
116: On Error GoTo -1
    On Error GoTo 0
    ActiveSheet.Range("A1", Range("A1").SpecialCells(xlLastCell)).Name = "REPORT_LOOKUP1_2"
    
    Range("A1").Select
    
    Application.EnableEvents = True

End Sub
Sub Remove_Empty_Data_Columns()

    Application.EnableEvents = False
    Application.DisplayAlerts = False
    Application.ScreenUpdating = False

    Sheets("Data").Select
    
Dim rng As Range
Dim cols As Variant
Dim cnt1 As Integer
  
    ReDim cols(0 To Cells(1, Sheets("Data").Columns.Count).End(xlToLeft).Column - 1)
    For cnt1 = 0 To UBound(cols)
    cols(cnt1) = cnt1 + 1
    Next cnt1
    
'    Set rng = Sheets("Data").Range("A1", Range("A1").SpecialCells(xlLastCell))
'    rng.RemoveDuplicates Columns:=(cols), Header:=xlYes
'    Set rng = Nothing
    
    ActiveWindow.ScrollRow = 1
    ActiveWindow.ScrollColumn = 1
    
    Application.EnableEvents = True
    
End Sub
